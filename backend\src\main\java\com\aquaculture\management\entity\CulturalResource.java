package com.aquaculture.management.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;

@Data
@Entity
@Table(name = "cultural_resource")
@TableName("cultural_resource")
@EqualsAndHashCode(callSuper = true)
public class CulturalResource extends BaseEntity {

    @NotBlank(message = "资源名称不能为空")
    private String name;

    @NotBlank(message = "资源类型不能为空")
    private String type;

    private String category;

    private String description;

    private String region;

    private String period;

    private String source;

    private String filePath;

    private String fileType;

    private Long fileSize;

    private String thumbnailPath;

    private String tags;

    private Integer viewCount;

    private Integer downloadCount;

    private Integer status;

    private String keywords;

    private String author;

    private String collector;
}