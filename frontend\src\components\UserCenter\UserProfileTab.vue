<template>
  <div class="user-profile-tab">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="昵称">
        {{ userProfile.nickname || '未设置' }}
      </el-descriptions-item>
      <el-descriptions-item label="性别">
        {{ getGenderText(userProfile.gender) }}
      </el-descriptions-item>
      <el-descriptions-item label="生日">
        {{ userProfile.birthday || '未设置' }}
      </el-descriptions-item>
      <el-descriptions-item label="职业">
        {{ userProfile.occupation || '未设置' }}
      </el-descriptions-item>
      <el-descriptions-item label="所在地" :span="2">
        {{ userProfile.location || '未设置' }}
      </el-descriptions-item>
      <el-descriptions-item label="个人简介" :span="2">
        {{ userProfile.bio || '这个人很懒，什么都没留下...' }}
      </el-descriptions-item>
      <el-descriptions-item label="兴趣爱好" :span="2">
        <div v-if="userProfile.interests">
          <el-tag
            v-for="interest in getInterests(userProfile.interests)"
            :key="interest"
            style="margin-right: 5px;"
          >
            {{ interest }}
          </el-tag>
        </div>
        <span v-else>未设置</span>
      </el-descriptions-item>
      <el-descriptions-item label="个人网站" :span="2">
        <a
          v-if="userProfile.website"
          :href="userProfile.website"
          target="_blank"
          class="website-link"
        >
          {{ userProfile.website }}
        </a>
        <span v-else>未设置</span>
      </el-descriptions-item>
      <el-descriptions-item label="隐私设置">
        {{ getPrivacyText(userProfile.privacyLevel) }}
      </el-descriptions-item>
      <el-descriptions-item label="注册时间">
        {{ formatTime(userProfile.createTime) }}
      </el-descriptions-item>
    </el-descriptions>

    <div class="profile-actions">
      <el-button type="primary" @click="$emit('update')">
        编辑资料
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { format } from 'date-fns'

const props = defineProps({
  userProfile: {
    type: Object,
    default: () => ({})
  }
})

defineEmits(['update'])

const getGenderText = (gender) => {
  const genderMap = {
    'male': '男',
    'female': '女',
    'other': '其他'
  }
  return genderMap[gender] || '未设置'
}

const getPrivacyText = (level) => {
  const privacyMap = {
    1: '公开',
    2: '好友可见',
    3: '仅自己可见'
  }
  return privacyMap[level] || '公开'
}

const getInterests = (interests) => {
  return interests ? interests.split(',').map(i => i.trim()).filter(i => i) : []
}

const formatTime = (time) => {
  if (!time) return '未知'
  try {
    return format(new Date(time), 'yyyy年MM月dd日')
  } catch (error) {
    return time
  }
}
</script>

<style scoped>
.user-profile-tab {
  padding: 20px 0;
}

.website-link {
  color: #409eff;
  text-decoration: none;
}

.website-link:hover {
  text-decoration: underline;
}

.profile-actions {
  margin-top: 30px;
  text-align: center;
}
</style>