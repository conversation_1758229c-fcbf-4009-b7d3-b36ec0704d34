# 水族文化资源管理平台

## 项目简介

水族文化资源管理平台是一个基于SpringBoot+Vue+MySQL8+Java11开发的水族文化传承与保护平台。旨在通过数字化手段收集、整理、保存和传播水族传统文化资源，促进水族文化的传承与发展。

## 技术栈

### 后端技术
- **Java 11** - 开发语言
- **Spring Boot 2.7.14** - 核心框架
- **Spring Security** - 安全框架
- **MyBatis Plus** - ORM框架
- **MySQL 8.0** - 数据库
- **JWT** - 身份认证
- **Maven** - 项目管理工具

### 前端技术
- **Vue 3** - 前端框架
- **Element Plus** - UI组件库
- **Vite** - 构建工具
- **Pinia** - 状态管理
- **Vue Router** - 路由管理
- **Axios** - HTTP客户端
- **ECharts** - 数据可视化

## 功能模块

### 1. 用户管理模块
- 用户注册、登录、注销
- 用户信息管理
- 角色权限管理
- 密码重置功能

### 2. 水族文化资源管理模块
- 文化资源录入、编辑、删除
- 资源分类管理（传统工艺、民歌、舞蹈、节庆等）
- 资源搜索与筛选
- 文件上传与下载
- 浏览统计功能

### 3. 文档资料管理模块
- 文档上传、编辑、删除
- 多格式文档支持（PDF、Word、文本等）
- 文档分类与标签管理
- 全文搜索功能
- 版本控制

### 4. 活动管理模块
- 文化活动创建与管理
- 活动报名与参与
- 活动状态跟踪
- 活动统计分析

### 5. 统计分析模块
- 数据统计报表
- 可视化图表展示
- 访问量统计
- 资源使用情况分析

## 项目结构

```
水族文化资源管理平台/
├── backend/                    # 后端项目
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/
│   │   │   │   └── com/aquaculture/management/
│   │   │   │       ├── config/          # 配置类
│   │   │   │       ├── controller/      # 控制器
│   │   │   │       ├── entity/          # 实体类
│   │   │   │       ├── enums/           # 枚举类
│   │   │   │       ├── mapper/          # 数据访问层
│   │   │   │       ├── service/         # 业务逻辑层
│   │   │   │       ├── utils/           # 工具类
│   │   │   │       ├── dto/             # 数据传输对象
│   │   │   │       └── vo/              # 视图对象
│   │   │   └── resources/
│   │   │       ├── application.yml      # 配置文件
│   │   │       ├── schema.sql           # 数据库结构
│   │   │       └── data.sql             # 初始数据
│   │   └── test/                        # 测试代码
│   └── pom.xml                          # Maven配置
│
├── frontend/                   # 前端项目
│   ├── src/
│   │   ├── api/                # API接口
│   │   ├── assets/             # 静态资源
│   │   ├── components/         # 公共组件
│   │   ├── router/             # 路由配置
│   │   ├── store/              # 状态管理
│   │   ├── utils/              # 工具函数
│   │   ├── views/              # 页面组件
│   │   ├── App.vue             # 根组件
│   │   └── main.js             # 入口文件
│   ├── index.html              # HTML模板
│   ├── package.json            # 依赖配置
│   └── vite.config.js          # Vite配置
│
└── README.md                   # 项目说明文档
```

## 快速开始

### 环境要求
- JDK 11+
- Node.js 16+
- MySQL 8.0+
- Maven 3.6+

### 后端启动

1. **配置数据库**
```sql
CREATE DATABASE aquaculture_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. **修改配置文件**
```yaml
# backend/src/main/resources/application.yml
spring:
  datasource:
    url: **************************************************
    username: your_username
    password: your_password
```

3. **运行数据库脚本**
```bash
# 执行 schema.sql 创建表结构
# 执行 data.sql 插入初始数据
```

4. **启动后端服务**
```bash
cd backend
mvn clean install
mvn spring-boot:run
```

后端服务将在 http://localhost:8080 启动

### 前端启动

1. **安装依赖**
```bash
cd frontend
npm install
```

2. **启动开发服务器**
```bash
npm run dev
```

前端服务将在 http://localhost:3000 启动

### 默认账户
- **管理员账户**: admin / admin123
- **普通用户账户**: user / admin123

## API文档

### 认证接口
```
POST /api/auth/login     # 用户登录
POST /api/auth/logout    # 用户登出
```

### 用户管理接口
```
GET    /api/users        # 获取用户列表
POST   /api/users        # 创建用户
PUT    /api/users/{id}   # 更新用户
DELETE /api/users/{id}   # 删除用户
```

### 文化资源接口
```
GET    /api/cultural-resources        # 获取资源列表
POST   /api/cultural-resources        # 创建资源
PUT    /api/cultural-resources/{id}   # 更新资源
DELETE /api/cultural-resources/{id}   # 删除资源
```

### 文档管理接口
```
GET    /api/documents        # 获取文档列表
POST   /api/documents        # 创建文档
PUT    /api/documents/{id}   # 更新文档
DELETE /api/documents/{id}   # 删除文档
```

### 活动管理接口
```
GET    /api/activities        # 获取活动列表
POST   /api/activities        # 创建活动
PUT    /api/activities/{id}   # 更新活动
DELETE /api/activities/{id}   # 删除活动
```

### 文件上传接口
```
POST   /api/files/upload           # 单文件上传
POST   /api/files/upload-multiple  # 多文件上传
DELETE /api/files/delete           # 删除文件
GET    /api/files/download         # 文件下载
```

## 部署说明

### 生产环境部署

1. **打包后端应用**
```bash
cd backend
mvn clean package -Pproduction
```

2. **打包前端应用**
```bash
cd frontend
npm run build
```

3. **部署到服务器**
```bash
# 部署后端 JAR 包
java -jar backend/target/aquaculture-management-1.0.0.jar

# 部署前端静态文件到 Nginx
cp -r frontend/dist/* /var/www/html/
```

### Docker 部署

1. **创建 Dockerfile**
```dockerfile
# 后端 Dockerfile
FROM openjdk:11-jre-slim
COPY target/aquaculture-management-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

2. **创建 docker-compose.yml**
```yaml
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: aquaculture_management
    ports:
      - "3306:3306"
  
  backend:
    build: ./backend
    ports:
      - "8080:8080"
    depends_on:
      - mysql
  
  frontend:
    image: nginx:alpine
    volumes:
      - ./frontend/dist:/usr/share/nginx/html
    ports:
      - "80:80"
```

## 功能特性

### 安全特性
- JWT 身份认证
- 基于角色的权限控制
- 请求加密与防篡改
- SQL注入防护
- XSS攻击防护

### 性能优化
- 数据库索引优化
- 分页查询
- 文件压缩上传
- CDN 资源加速
- 缓存策略

### 用户体验
- 响应式设计
- 国际化支持
- 操作日志记录
- 错误提示优化
- 加载状态提示

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 开发规范

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用ESLint进行前端代码检查
- 统一代码格式化配置

### 提交规范
```
feat: 新功能
fix: 修复问题
docs: 文档修改
style: 代码格式修改
refactor: 代码重构
test: 测试用例修改
chore: 其他修改
```

### 分支策略
- `main`: 主分支，用于生产环境
- `develop`: 开发分支，用于集成测试
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

## 常见问题

### Q1: 启动时出现数据库连接错误？
A: 请检查MySQL服务是否启动，数据库配置是否正确。

### Q2: 前端页面显示异常？
A: 请检查后端服务是否正常启动，API接口是否可访问。

### Q3: 文件上传失败？
A: 请检查上传文件大小是否超过限制（50MB），文件格式是否支持。

### Q4: 登录后页面空白？
A: 请清除浏览器缓存，检查JWT token是否有效。

## 版本历史

- **v1.0.0** (2024-01-15)
  - 初始版本发布
  - 实现基础功能模块
  - 完成用户管理和资源管理

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目维护者：开发团队
- 邮箱：<EMAIL>
- 项目地址：https://github.com/aquaculture/management-platform

## 致谢

感谢以下开源项目：
- Spring Boot
- Vue.js
- Element Plus
- MyBatis Plus
- ECharts

---

> 传承水族文化，弘扬民族精神 🌊