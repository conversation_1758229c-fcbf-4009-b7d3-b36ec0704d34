<template>
  <div class="audit-detail">
    <div class="audit-info">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="内容标题">
          <div class="title-info">
            <span>{{ audit.contentTitle }}</span>
            <el-tag :type="getContentTypeColor(audit.contentType)" size="small">
              {{ getContentTypeName(audit.contentType) }}
            </el-tag>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="提交人">
          {{ audit.submitUserName }}
        </el-descriptions-item>
        <el-descriptions-item label="提交时间">
          {{ formatTime(audit.submitTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="优先级">
          <el-tag :type="getPriorityColor(audit.priority)" size="small">
            {{ getPriorityName(audit.priority) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="审核状态">
          <el-tag :type="getStatusColor(audit.auditStatus)" size="small">
            {{ getStatusName(audit.auditStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="风险等级">
          <el-tag :type="getRiskColor(audit.riskLevel)" size="small">
            {{ getRiskName(audit.riskLevel) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="自动评分" v-if="audit.autoAuditScore !== null">
          <div class="score-info">
            <el-progress
              :percentage="audit.autoAuditScore"
              :color="getScoreColor(audit.autoAuditScore)"
              :stroke-width="8"
              style="width: 200px;"
            />
            <span class="score-text">{{ audit.autoAuditScore }}/100</span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="是否需要人工审核">
          <el-tag :type="audit.manualRequired ? 'warning' : 'success'" size="small">
            {{ audit.manualRequired ? '是' : '否' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="content-summary" v-if="audit.contentSummary">
      <h4>内容摘要</h4>
      <div class="summary-content">
        {{ audit.contentSummary }}
      </div>
    </div>

    <div class="keywords" v-if="audit.keywords">
      <h4>关键词</h4>
      <div class="keywords-content">
        <el-tag
          v-for="keyword in getKeywords(audit.keywords)"
          :key="keyword"
          size="small"
          style="margin-right: 8px; margin-bottom: 8px;"
        >
          {{ keyword }}
        </el-tag>
      </div>
    </div>

    <div class="audit-history" v-if="audit.auditStatus !== 0">
      <h4>审核记录</h4>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="审核人">
          {{ audit.auditUserName }}
        </el-descriptions-item>
        <el-descriptions-item label="审核时间">
          {{ formatTime(audit.auditTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="审核意见">
          {{ audit.auditReason || '无' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="audit-actions" v-if="audit.auditStatus === 0">
      <el-form :model="auditForm" label-width="100px">
        <el-form-item label="审核结果">
          <el-radio-group v-model="auditForm.result">
            <el-radio label="approve">通过</el-radio>
            <el-radio label="reject">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见">
          <el-input
            v-model="auditForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入审核意见"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <div class="action-buttons">
        <el-button @click="$emit('close')">取消</el-button>
        <el-button
          v-if="audit.autoAuditScore >= 60"
          type="warning"
          @click="handleAutoAudit"
          :loading="autoLoading"
        >
          自动审核
        </el-button>
        <el-button
          type="primary"
          @click="handleSubmitAudit"
          :loading="submitLoading"
        >
          提交审核
        </el-button>
      </div>
    </div>

    <div class="view-content" v-if="audit.auditStatus === 0">
      <el-button type="info" @click="viewOriginalContent">
        查看原始内容
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'
import { format } from 'date-fns'

const props = defineProps({
  audit: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['approve', 'reject', 'close'])

const submitLoading = ref(false)
const autoLoading = ref(false)

const auditForm = reactive({
  result: 'approve',
  reason: ''
})

const handleSubmitAudit = async () => {
  if (auditForm.result === 'reject' && !auditForm.reason.trim()) {
    ElMessage.warning('拒绝审核时必须填写审核意见')
    return
  }

  submitLoading.value = true
  try {
    if (auditForm.result === 'approve') {
      emit('approve', props.audit, auditForm.reason)
    } else {
      emit('reject', props.audit, auditForm.reason)
    }
  } catch (error) {
    ElMessage.error('审核提交失败')
  } finally {
    submitLoading.value = false
  }
}

const handleAutoAudit = async () => {
  try {
    await ElMessageBox.confirm('确定要执行自动审核吗？', '自动审核', {
      type: 'warning'
    })

    autoLoading.value = true
    await request.post(`/content-audit/auto/${props.audit.id}`)
    ElMessage.success('自动审核完成')
    emit('close')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('自动审核失败')
    }
  } finally {
    autoLoading.value = false
  }
}

const viewOriginalContent = () => {
  const { contentType, contentId } = props.audit
  let route = ''
  
  switch (contentType) {
    case 'cultural_resource':
      route = `/cultural-resources/${contentId}`
      break
    case 'document':
      route = `/documents/${contentId}`
      break
    case 'activity':
      route = `/activities/${contentId}`
      break
    default:
      ElMessage.info('暂不支持查看该类型内容')
      return
  }
  
  window.open(route, '_blank')
}

// 辅助方法
const getContentTypeName = (type) => {
  const typeMap = {
    'cultural_resource': '文化资源',
    'document': '文档资料',
    'activity': '活动'
  }
  return typeMap[type] || type
}

const getContentTypeColor = (type) => {
  const colorMap = {
    'cultural_resource': 'primary',
    'document': 'success',
    'activity': 'warning'
  }
  return colorMap[type] || 'info'
}

const getPriorityName = (priority) => {
  const priorityMap = {
    1: '低',
    2: '中',
    3: '高',
    4: '紧急'
  }
  return priorityMap[priority] || '未知'
}

const getPriorityColor = (priority) => {
  const colorMap = {
    1: 'info',
    2: 'primary',
    3: 'warning',
    4: 'danger'
  }
  return colorMap[priority] || 'info'
}

const getStatusName = (status) => {
  const statusMap = {
    0: '待审核',
    1: '已通过',
    2: '已拒绝'
  }
  return statusMap[status] || '未知'
}

const getStatusColor = (status) => {
  const colorMap = {
    0: 'warning',
    1: 'success',
    2: 'danger'
  }
  return colorMap[status] || 'info'
}

const getRiskName = (level) => {
  const riskMap = {
    1: '低风险',
    2: '中风险',
    3: '高风险'
  }
  return riskMap[level] || '未知'
}

const getRiskColor = (level) => {
  const colorMap = {
    1: 'success',
    2: 'warning',
    3: 'danger'
  }
  return colorMap[level] || 'info'
}

const getScoreColor = (score) => {
  if (score >= 80) return '#67c23a'
  if (score >= 60) return '#e6a23c'
  return '#f56c6c'
}

const getKeywords = (keywords) => {
  return keywords ? keywords.split(',').map(k => k.trim()).filter(k => k) : []
}

const formatTime = (time) => {
  if (!time) return '-'
  try {
    return format(new Date(time), 'yyyy-MM-dd HH:mm:ss')
  } catch (error) {
    return time
  }
}
</script>

<style scoped>
.audit-detail {
  padding: 20px 0;
}

.audit-info {
  margin-bottom: 30px;
}

.title-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.score-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.score-text {
  font-weight: 500;
  color: #333;
}

.content-summary,
.keywords,
.audit-history {
  margin-bottom: 30px;
}

.content-summary h4,
.keywords h4,
.audit-history h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.summary-content {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  line-height: 1.6;
  color: #555;
  border-left: 4px solid #409eff;
}

.keywords-content {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #67c23a;
}

.audit-actions {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.view-content {
  text-align: center;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}
</style>