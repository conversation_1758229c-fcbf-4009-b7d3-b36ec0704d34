// 响应式设计样式文件

// 断点定义
$breakpoints: (
  xs: 480px,
  sm: 768px,
  md: 992px,
  lg: 1200px,
  xl: 1600px
);

// 媒体查询混合器
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (max-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Invalid breakpoint: #{$breakpoint}";
  }
}

// 移动端优先的响应式混合器
@mixin respond-above($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}

// 全局响应式样式
body {
  @include respond-to(sm) {
    font-size: 14px;
  }
  
  @include respond-to(xs) {
    font-size: 12px;
  }
}

// 容器响应式
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  
  @include respond-to(md) {
    padding: 0 15px;
  }
  
  @include respond-to(sm) {
    padding: 0 10px;
  }
}

// 布局响应式
.layout-container {
  @include respond-to(sm) {
    .layout-main {
      flex-direction: column;
    }
    
    .layout-aside {
      width: 100% !important;
      order: 2;
      
      .sidebar-menu {
        display: flex;
        overflow-x: auto;
        
        .el-menu-item {
          min-width: 120px;
          justify-content: center;
        }
      }
    }
    
    .layout-content {
      order: 1;
      margin-left: 0 !important;
      padding: 10px;
    }
  }
}

// 表格响应式
.responsive-table {
  @include respond-to(sm) {
    .el-table {
      font-size: 12px;
      
      .el-table__header {
        display: none;
      }
      
      .el-table__body {
        tr {
          display: block;
          border: 1px solid #ebeef5;
          margin-bottom: 10px;
          border-radius: 6px;
          
          td {
            display: block;
            text-align: left !important;
            border: none;
            padding: 8px 15px;
            position: relative;
            
            &:before {
              content: attr(data-label) ": ";
              font-weight: bold;
              color: #606266;
              margin-right: 10px;
            }
            
            &.el-table__cell {
              border-bottom: 1px solid #f5f5f5;
              
              &:last-child {
                border-bottom: none;
              }
            }
          }
        }
      }
    }
  }
}

// 表单响应式
.responsive-form {
  @include respond-to(sm) {
    .el-form-item {
      margin-bottom: 15px;
      
      .el-form-item__label {
        text-align: left !important;
        padding-bottom: 5px;
      }
      
      .el-form-item__content {
        margin-left: 0 !important;
      }
    }
    
    .el-row {
      .el-col {
        width: 100% !important;
        margin-bottom: 10px;
      }
    }
  }
}

// 卡片响应式
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  
  @include respond-to(md) {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 15px;
  }
  
  @include respond-to(sm) {
    grid-template-columns: 1fr;
    gap: 10px;
  }
}

// 按钮组响应式
.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  
  @include respond-to(sm) {
    flex-direction: column;
    
    .el-button {
      width: 100%;
      margin: 0 0 8px 0 !important;
    }
  }
}

// 导航响应式
.nav-responsive {
  @include respond-to(sm) {
    .el-menu--horizontal {
      .el-menu-item {
        height: 40px;
        line-height: 40px;
        font-size: 12px;
        padding: 0 10px;
      }
    }
  }
}

// 分页响应式
.pagination-responsive {
  @include respond-to(sm) {
    .el-pagination {
      text-align: center;
      
      .el-pagination__sizes,
      .el-pagination__jump {
        display: none !important;
      }
      
      .el-pager {
        .number {
          display: none;
          
          &.active,
          &:first-child,
          &:last-child {
            display: inline-block;
          }
        }
      }
    }
  }
}

// 对话框响应式
@include respond-to(sm) {
  .el-dialog {
    width: 95% !important;
    margin: 5% auto !important;
    
    .el-dialog__header {
      padding: 15px;
    }
    
    .el-dialog__body {
      padding: 15px;
      max-height: 60vh;
      overflow-y: auto;
    }
    
    .el-dialog__footer {
      padding: 15px;
      text-align: center;
      
      .el-button {
        margin: 5px;
        width: 45%;
      }
    }
  }
}

// 抽屉响应式
@include respond-to(sm) {
  .el-drawer {
    width: 100% !important;
  }
}

// 消息提示响应式
@include respond-to(sm) {
  .el-message {
    min-width: 280px !important;
    max-width: 90% !important;
  }
  
  .el-notification {
    width: 90% !important;
    right: 5% !important;
  }
}

// 图片响应式
.responsive-image {
  max-width: 100%;
  height: auto;
  display: block;
}

// 文本响应式
.text-responsive {
  @include respond-to(sm) {
    font-size: 14px;
    line-height: 1.5;
  }
  
  @include respond-to(xs) {
    font-size: 12px;
    line-height: 1.4;
  }
}

// 标题响应式
.title-responsive {
  @include respond-to(sm) {
    h1 { font-size: 24px; }
    h2 { font-size: 20px; }
    h3 { font-size: 18px; }
    h4 { font-size: 16px; }
    h5 { font-size: 14px; }
    h6 { font-size: 12px; }
  }
}

// 间距响应式
.spacing-responsive {
  @include respond-to(sm) {
    padding: 10px !important;
    margin: 10px 0 !important;
  }
}

// 隐藏元素
.hidden-xs {
  @include respond-to(xs) {
    display: none !important;
  }
}

.hidden-sm {
  @include respond-to(sm) {
    display: none !important;
  }
}

.hidden-md {
  @include respond-to(md) {
    display: none !important;
  }
}

// 显示元素
.visible-xs {
  display: none !important;
  
  @include respond-to(xs) {
    display: block !important;
  }
}

.visible-sm {
  display: none !important;
  
  @include respond-to(sm) {
    display: block !important;
  }
}

.visible-md {
  display: none !important;
  
  @include respond-to(md) {
    display: block !important;
  }
}

// 触摸友好
.touch-friendly {
  @include respond-to(sm) {
    .el-button {
      min-height: 44px;
      padding: 12px 20px;
    }
    
    .el-input__inner {
      height: 44px;
      line-height: 44px;
    }
    
    .el-select {
      .el-input__inner {
        height: 44px;
        line-height: 44px;
      }
    }
    
    .clickable {
      min-height: 44px;
      display: flex;
      align-items: center;
    }
  }
}

// 滚动优化
.scroll-optimized {
  @include respond-to(sm) {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
  }
}

// Flex 布局响应式
.flex-responsive {
  display: flex;
  
  @include respond-to(sm) {
    flex-direction: column;
    
    &.flex-wrap {
      flex-wrap: wrap;
    }
    
    &.flex-center {
      align-items: center;
      justify-content: center;
    }
  }
}

// 网格布局响应式
.grid-responsive {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  
  @include respond-to(lg) {
    grid-template-columns: repeat(3, 1fr);
  }
  
  @include respond-to(md) {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }
  
  @include respond-to(sm) {
    grid-template-columns: 1fr;
    gap: 10px;
  }
}