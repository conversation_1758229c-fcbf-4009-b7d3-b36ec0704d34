package com.aquaculture.management.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "statistics")
@TableName("statistics")
@EqualsAndHashCode(callSuper = true)
public class Statistics extends BaseEntity {

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate statisticsDate;

    private String type;

    private String category;

    private Integer totalCount;

    private Integer newCount;

    private Integer activeCount;

    private Integer viewCount;

    private Integer downloadCount;

    private String region;

    private String details;
}