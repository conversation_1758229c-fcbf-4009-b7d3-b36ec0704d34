package com.aquaculture.management.service.impl;

import com.aquaculture.management.dto.Result;
import com.aquaculture.management.entity.Comment;
import com.aquaculture.management.entity.CommentLike;
import com.aquaculture.management.mapper.CommentMapper;
import com.aquaculture.management.mapper.CommentLikeMapper;
import com.aquaculture.management.service.CommentService;
import com.aquaculture.management.utils.UserContext;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class CommentServiceImpl extends ServiceImpl<CommentMapper, Comment> implements CommentService {

    private final CommentLikeMapper commentLikeMapper;
    private final HttpServletRequest request;

    @Override
    @Transactional
    public Result<Comment> addComment(String targetType, Long targetId, String content, 
                                    Long parentId, Long replyToUserId) {
        try {
            Comment comment = new Comment();
            comment.setContent(content);
            comment.setTargetType(targetType);
            comment.setTargetId(targetId);
            comment.setUserId(UserContext.getCurrentUserId());
            comment.setUserName(UserContext.getCurrentUserName());
            comment.setParentId(parentId);
            comment.setReplyToUserId(replyToUserId);
            comment.setStatus(1); // 正常状态
            comment.setAuditStatus(1); // 默认审核通过，也可以设为待审核
            comment.setIpAddress(getClientIpAddress());
            comment.setUserAgent(request.getHeader("User-Agent"));

            // 如果是回复评论，设置回复用户名
            if (replyToUserId != null) {
                // 这里需要查询用户信息获取用户名，简化处理
                comment.setReplyToUserName("用户" + replyToUserId);
            }

            save(comment);

            // 如果是回复，更新父评论的回复数量
            if (parentId != null) {
                Comment parentComment = getById(parentId);
                if (parentComment != null) {
                    parentComment.setReplyCount(parentComment.getReplyCount() + 1);
                    updateById(parentComment);
                }
            }

            return Result.success(comment);
        } catch (Exception e) {
            log.error("发表评论失败", e);
            return Result.error("发表评论失败: " + e.getMessage());
        }
    }

    @Override
    public Result<IPage<Comment>> getComments(String targetType, Long targetId, 
                                            Integer current, Integer size, String sortBy) {
        try {
            Page<Comment> page = new Page<>(current, size);
            
            LambdaQueryWrapper<Comment> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Comment::getTargetType, targetType)
                   .eq(Comment::getTargetId, targetId)
                   .eq(Comment::getStatus, 1)
                   .eq(Comment::getAuditStatus, 1)
                   .isNull(Comment::getParentId) // 只查询顶级评论
                   .eq(Comment::getDeleted, 0);

            // 排序
            if ("hot".equals(sortBy)) {
                wrapper.orderByDesc(Comment::getLikeCount)
                       .orderByDesc(Comment::getCreateTime);
            } else {
                wrapper.orderByDesc(Comment::getIsPinned)
                       .orderByDesc(Comment::getCreateTime);
            }

            IPage<Comment> result = page(page, wrapper);
            
            // 为每个评论检查当前用户是否已点赞
            if (UserContext.isAuthenticated()) {
                result.getRecords().forEach(this::setUserLikeStatus);
            }

            return Result.success(result);
        } catch (Exception e) {
            log.error("获取评论列表失败", e);
            return Result.error("获取评论列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<Comment>> getReplies(Long parentId) {
        try {
            LambdaQueryWrapper<Comment> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Comment::getParentId, parentId)
                   .eq(Comment::getStatus, 1)
                   .eq(Comment::getAuditStatus, 1)
                   .eq(Comment::getDeleted, 0)
                   .orderByAsc(Comment::getCreateTime);

            List<Comment> replies = list(wrapper);
            
            // 为每个回复检查当前用户是否已点赞
            if (UserContext.isAuthenticated()) {
                replies.forEach(this::setUserLikeStatus);
            }

            return Result.success(replies);
        } catch (Exception e) {
            log.error("获取回复列表失败", e);
            return Result.error("获取回复列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> deleteComment(Long commentId) {
        try {
            Comment comment = getById(commentId);
            if (comment == null) {
                return Result.error("评论不存在");
            }

            // 检查权限：只有评论作者或管理员可以删除
            Long currentUserId = UserContext.getCurrentUserId();
            if (!comment.getUserId().equals(currentUserId) && !UserContext.hasRole("ADMIN")) {
                return Result.error("无权限删除此评论");
            }

            // 软删除
            comment.setDeleted(1);
            updateById(comment);

            // 删除所有回复
            LambdaQueryWrapper<Comment> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Comment::getParentId, commentId);
            List<Comment> replies = list(wrapper);
            
            for (Comment reply : replies) {
                reply.setDeleted(1);
                updateById(reply);
            }

            return Result.success();
        } catch (Exception e) {
            log.error("删除评论失败", e);
            return Result.error("删除评论失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> likeComment(Long commentId, Integer likeType) {
        try {
            Long userId = UserContext.getCurrentUserId();
            
            // 检查是否已经点赞/点踩
            LambdaQueryWrapper<CommentLike> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CommentLike::getCommentId, commentId)
                   .eq(CommentLike::getUserId, userId)
                   .eq(CommentLike::getDeleted, 0);
            
            CommentLike existingLike = commentLikeMapper.selectOne(wrapper);
            
            Comment comment = getById(commentId);
            if (comment == null) {
                return Result.error("评论不存在");
            }

            if (existingLike != null) {
                if (existingLike.getLikeType().equals(likeType)) {
                    // 取消点赞/点踩
                    existingLike.setDeleted(1);
                    commentLikeMapper.updateById(existingLike);
                    
                    if (likeType == 1) {
                        comment.setLikeCount(Math.max(0, comment.getLikeCount() - 1));
                    }
                } else {
                    // 改变点赞类型
                    existingLike.setLikeType(likeType);
                    commentLikeMapper.updateById(existingLike);
                    
                    if (likeType == 1) {
                        comment.setLikeCount(comment.getLikeCount() + 1);
                    } else {
                        comment.setLikeCount(Math.max(0, comment.getLikeCount() - 1));
                    }
                }
            } else {
                // 新增点赞/点踩
                CommentLike commentLike = new CommentLike();
                commentLike.setCommentId(commentId);
                commentLike.setUserId(userId);
                commentLike.setLikeType(likeType);
                commentLikeMapper.insert(commentLike);
                
                if (likeType == 1) {
                    comment.setLikeCount(comment.getLikeCount() + 1);
                }
            }

            updateById(comment);
            return Result.success();
        } catch (Exception e) {
            log.error("点赞评论失败", e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> pinComment(Long commentId, Boolean isPinned) {
        try {
            Comment comment = getById(commentId);
            if (comment == null) {
                return Result.error("评论不存在");
            }

            // 检查权限：只有管理员可以置顶
            if (!UserContext.hasRole("ADMIN")) {
                return Result.error("无权限执行此操作");
            }

            comment.setIsPinned(isPinned);
            updateById(comment);

            return Result.success();
        } catch (Exception e) {
            log.error("置顶评论失败", e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Long> getCommentCount(String targetType, Long targetId) {
        try {
            LambdaQueryWrapper<Comment> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Comment::getTargetType, targetType)
                   .eq(Comment::getTargetId, targetId)
                   .eq(Comment::getStatus, 1)
                   .eq(Comment::getAuditStatus, 1)
                   .eq(Comment::getDeleted, 0);

            long count = count(wrapper);
            return Result.success(count);
        } catch (Exception e) {
            log.error("获取评论数量失败", e);
            return Result.error("获取评论数量失败");
        }
    }

    @Override
    @Transactional
    public Result<Void> auditComment(Long commentId, Integer auditStatus, String auditReason) {
        try {
            Comment comment = getById(commentId);
            if (comment == null) {
                return Result.error("评论不存在");
            }

            // 检查权限：只有管理员可以审核
            if (!UserContext.hasRole("ADMIN")) {
                return Result.error("无权限执行此操作");
            }

            comment.setAuditStatus(auditStatus);
            comment.setAuditReason(auditReason);
            comment.setAuditTime(LocalDateTime.now());
            updateById(comment);

            return Result.success();
        } catch (Exception e) {
            log.error("审核评论失败", e);
            return Result.error("审核失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> batchDeleteComments(List<Long> commentIds) {
        try {
            // 检查权限：只有管理员可以批量删除
            if (!UserContext.hasRole("ADMIN")) {
                return Result.error("无权限执行此操作");
            }

            for (Long commentId : commentIds) {
                deleteComment(commentId);
            }

            return Result.success();
        } catch (Exception e) {
            log.error("批量删除评论失败", e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    @Override
    public Result<IPage<Comment>> getUserComments(Long userId, Integer current, Integer size) {
        try {
            Page<Comment> page = new Page<>(current, size);
            
            LambdaQueryWrapper<Comment> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Comment::getUserId, userId)
                   .eq(Comment::getStatus, 1)
                   .eq(Comment::getDeleted, 0)
                   .orderByDesc(Comment::getCreateTime);

            IPage<Comment> result = page(page, wrapper);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取用户评论失败", e);
            return Result.error("获取用户评论失败");
        }
    }

    @Override
    public Result<Boolean> hasLikedComment(Long commentId) {
        try {
            if (!UserContext.isAuthenticated()) {
                return Result.success(false);
            }

            Long userId = UserContext.getCurrentUserId();
            
            LambdaQueryWrapper<CommentLike> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CommentLike::getCommentId, commentId)
                   .eq(CommentLike::getUserId, userId)
                   .eq(CommentLike::getLikeType, 1)
                   .eq(CommentLike::getDeleted, 0);

            long count = commentLikeMapper.selectCount(wrapper);
            return Result.success(count > 0);
        } catch (Exception e) {
            log.error("检查点赞状态失败", e);
            return Result.success(false);
        }
    }

    private void setUserLikeStatus(Comment comment) {
        try {
            Result<Boolean> hasLiked = hasLikedComment(comment.getId());
            comment.setHasLiked(hasLiked.getData());
        } catch (Exception e) {
            log.error("设置用户点赞状态失败", e);
            comment.setHasLiked(false);
        }
    }

    private String getClientIpAddress() {
        try {
            String xForwardedFor = request.getHeader("X-Forwarded-For");
            if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
                return xForwardedFor.split(",")[0].trim();
            }
            
            String xRealIp = request.getHeader("X-Real-IP");
            if (xRealIp != null && !xRealIp.isEmpty()) {
                return xRealIp;
            }
            
            return request.getRemoteAddr();
        } catch (Exception e) {
            return "unknown";
        }
    }
}