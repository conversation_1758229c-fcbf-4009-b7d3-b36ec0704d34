@echo off
chcp 65001
echo ========================================
echo    水族文化资源管理平台启动脚本
echo ========================================
echo.

echo [1] 检查环境...
where java >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误：未找到Java环境，请先安装JDK 11+
    pause
    exit /b
)

where node >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误：未找到Node.js环境，请先安装Node.js 16+
    pause
    exit /b
)

echo Java环境检查通过
echo Node.js环境检查通过
echo.

echo [2] 启动后端服务...
cd backend
echo 正在编译后端项目...
call mvn clean compile -q
if %errorlevel% neq 0 (
    echo 错误：后端编译失败
    pause
    exit /b
)

echo 正在启动SpringBoot服务...
start "水族文化平台-后端" cmd /c "mvn spring-boot:run"
echo 后端服务启动中，请等待...
timeout /t 10 /nobreak >nul

cd ..\frontend
echo.
echo [3] 启动前端服务...

if not exist node_modules (
    echo 正在安装前端依赖...
    call npm install
    if %errorlevel% neq 0 (
        echo 错误：前端依赖安装失败
        pause
        exit /b
    )
)

echo 正在启动前端开发服务器...
start "水族文化平台-前端" cmd /c "npm run dev"

echo.
echo ========================================
echo 服务启动完成！
echo ========================================
echo 后端服务：http://localhost:8080
echo 前端服务：http://localhost:3000
echo.
echo 默认管理员账户：
echo 用户名：admin
echo 密码：admin123
echo.
echo 请等待服务完全启动后再访问前端页面
echo ========================================

pause