package com.aquaculture.management.service;

import com.aquaculture.management.entity.Document;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface DocumentService extends IService<Document> {

    IPage<Document> getDocuments(int current, int size, String keyword, String type, String category);

    boolean createDocument(Document document);

    boolean updateDocument(Document document);

    boolean deleteDocument(Long id);

    void incrementViewCount(Long id);

    void incrementDownloadCount(Long id);

    List<Document> getRecentDocuments(int limit);
}