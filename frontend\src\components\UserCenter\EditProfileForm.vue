<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-width="100px"
  >
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="formData.nickname" placeholder="请输入昵称" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="formData.gender">
            <el-radio label="male">男</el-radio>
            <el-radio label="female">女</el-radio>
            <el-radio label="other">其他</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="生日" prop="birthday">
          <el-date-picker
            v-model="formData.birthday"
            type="date"
            placeholder="选择生日"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="职业" prop="occupation">
          <el-input v-model="formData.occupation" placeholder="请输入职业" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="所在地" prop="location">
      <el-input v-model="formData.location" placeholder="请输入所在地" />
    </el-form-item>

    <el-form-item label="个人简介" prop="bio">
      <el-input
        v-model="formData.bio"
        type="textarea"
        :rows="4"
        placeholder="介绍一下自己吧..."
        maxlength="200"
        show-word-limit
      />
    </el-form-item>

    <el-form-item label="兴趣爱好" prop="interests">
      <el-input
        v-model="formData.interests"
        placeholder="用逗号分隔多个兴趣爱好"
      />
    </el-form-item>

    <el-form-item label="个人网站" prop="website">
      <el-input v-model="formData.website" placeholder="请输入个人网站" />
    </el-form-item>

    <el-form-item label="社交媒体" prop="socialMedia">
      <el-input v-model="formData.socialMedia" placeholder="请输入社交媒体账号" />
    </el-form-item>

    <el-form-item label="隐私设置" prop="privacyLevel">
      <el-radio-group v-model="formData.privacyLevel">
        <el-radio :label="1">公开</el-radio>
        <el-radio :label="2">好友可见</el-radio>
        <el-radio :label="3">仅自己可见</el-radio>
      </el-radio-group>
    </el-form-item>

    <el-form-item>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
        保存
      </el-button>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="danger" @click="showPasswordDialog = true">
        修改密码
      </el-button>
    </el-form-item>
  </el-form>

  <!-- 修改密码对话框 -->
  <el-dialog v-model="showPasswordDialog" title="修改密码" width="400px">
    <el-form
      ref="passwordFormRef"
      :model="passwordForm"
      :rules="passwordRules"
      label-width="100px"
    >
      <el-form-item label="原密码" prop="oldPassword">
        <el-input
          v-model="passwordForm.oldPassword"
          type="password"
          show-password
          placeholder="请输入原密码"
        />
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input
          v-model="passwordForm.newPassword"
          type="password"
          show-password
          placeholder="请输入新密码"
        />
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input
          v-model="passwordForm.confirmPassword"
          type="password"
          show-password
          placeholder="请确认新密码"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="showPasswordDialog = false">取消</el-button>
      <el-button type="primary" @click="handlePasswordChange" :loading="passwordLoading">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'

const props = defineProps({
  userProfile: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['success', 'cancel'])

const formRef = ref()
const passwordFormRef = ref()
const submitLoading = ref(false)
const passwordLoading = ref(false)
const showPasswordDialog = ref(false)

const formData = reactive({
  nickname: '',
  gender: '',
  birthday: '',
  occupation: '',
  location: '',
  bio: '',
  interests: '',
  website: '',
  socialMedia: '',
  privacyLevel: 1
})

const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const formRules = {
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '昵称长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  bio: [
    { max: 200, message: '个人简介不能超过200个字符', trigger: 'blur' }
  ]
}

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      try {
        await request.put('/user-center/profile', formData)
        ElMessage.success('资料更新成功')
        emit('success')
      } catch (error) {
        ElMessage.error('更新失败：' + error.message)
      } finally {
        submitLoading.value = false
      }
    }
  })
}

const handleCancel = () => {
  emit('cancel')
}

const handlePasswordChange = async () => {
  if (!passwordFormRef.value) return
  
  await passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      passwordLoading.value = true
      try {
        await request.post('/user-center/change-password', null, {
          params: {
            oldPassword: passwordForm.oldPassword,
            newPassword: passwordForm.newPassword
          }
        })
        ElMessage.success('密码修改成功')
        showPasswordDialog.value = false
        resetPasswordForm()
      } catch (error) {
        ElMessage.error('密码修改失败：' + error.message)
      } finally {
        passwordLoading.value = false
      }
    }
  })
}

const resetPasswordForm = () => {
  Object.assign(passwordForm, {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  if (passwordFormRef.value) {
    passwordFormRef.value.resetFields()
  }
}

// 监听props变化，更新表单数据
watch(() => props.userProfile, (newProfile) => {
  if (newProfile) {
    Object.assign(formData, {
      nickname: newProfile.nickname || '',
      gender: newProfile.gender || '',
      birthday: newProfile.birthday || '',
      occupation: newProfile.occupation || '',
      location: newProfile.location || '',
      bio: newProfile.bio || '',
      interests: newProfile.interests || '',
      website: newProfile.website || '',
      socialMedia: newProfile.socialMedia || '',
      privacyLevel: newProfile.privacyLevel || 1
    })
  }
}, { immediate: true, deep: true })
</script>

<style scoped>
.el-form {
  max-width: 600px;
}
</style>