<template>
  <div class="comment-section">
    <div class="comment-header">
      <h3>评论 ({{ commentCount }})</h3>
      <div class="sort-options">
        <el-radio-group v-model="sortBy" size="small" @change="loadComments">
          <el-radio-button label="time">时间排序</el-radio-button>
          <el-radio-button label="hot">热度排序</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 发表评论 -->
    <div class="comment-form" v-if="userStore.token">
      <div class="form-header">
        <el-avatar :size="40" :src="userStore.userInfo?.avatar">
          <el-icon><User /></el-icon>
        </el-avatar>
        <span class="username">{{ userStore.userInfo?.realName || userStore.userInfo?.username }}</span>
      </div>
      <el-input
        v-model="newComment"
        type="textarea"
        :rows="3"
        placeholder="写下你的评论..."
        maxlength="500"
        show-word-limit
        @focus="showSubmitButton = true"
      />
      <div class="form-actions" v-show="showSubmitButton">
        <el-button @click="cancelComment">取消</el-button>
        <el-button type="primary" @click="submitComment" :loading="submitting">
          发表评论
        </el-button>
      </div>
    </div>

    <div class="login-prompt" v-else>
      <el-alert title="请登录后发表评论" type="info" show-icon />
    </div>

    <!-- 评论列表 -->
    <div class="comment-list" v-loading="loading">
      <div v-if="comments.length === 0 && !loading" class="empty-comments">
        <el-empty description="暂无评论，快来发表第一条评论吧！" />
      </div>

      <div
        v-for="comment in comments"
        :key="comment.id"
        class="comment-item"
        :class="{ 'pinned': comment.isPinned }"
      >
        <div class="comment-avatar">
          <el-avatar :size="40" :src="comment.userAvatar">
            <el-icon><User /></el-icon>
          </el-avatar>
        </div>

        <div class="comment-content">
          <div class="comment-header">
            <div class="user-info">
              <span class="username">{{ comment.userName }}</span>
              <span class="comment-time">{{ formatTime(comment.createTime) }}</span>
              <el-tag v-if="comment.isPinned" type="warning" size="small">置顶</el-tag>
            </div>
            <div class="comment-actions">
              <el-dropdown @command="handleCommentAction" trigger="click">
                <el-button text size="small">
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item 
                      v-if="canManageComment(comment)"
                      :command="{action: 'pin', comment}"
                    >
                      {{ comment.isPinned ? '取消置顶' : '置顶' }}
                    </el-dropdown-item>
                    <el-dropdown-item 
                      v-if="canDeleteComment(comment)"
                      :command="{action: 'delete', comment}"
                    >
                      删除
                    </el-dropdown-item>
                    <el-dropdown-item :command="{action: 'report', comment}">
                      举报
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>

          <div class="comment-text">
            <p v-if="comment.replyToUserName" class="reply-to">
              回复 <span class="reply-user">@{{ comment.replyToUserName }}</span>:
            </p>
            <p>{{ comment.content }}</p>
          </div>

          <div class="comment-footer">
            <div class="comment-stats">
              <el-button
                text
                size="small"
                :class="{ 'liked': comment.hasLiked }"
                @click="likeComment(comment)"
              >
                <el-icon><CaretTop /></el-icon>
                {{ comment.likeCount || 0 }}
              </el-button>
              <el-button text size="small" @click="replyToComment(comment)">
                <el-icon><ChatRound /></el-icon>
                回复
              </el-button>
            </div>
          </div>

          <!-- 回复框 -->
          <div v-if="replyingTo === comment.id" class="reply-form">
            <el-input
              v-model="replyContent"
              type="textarea"
              :rows="2"
              :placeholder="`回复 @${comment.userName}:`"
              maxlength="500"
              show-word-limit
            />
            <div class="reply-actions">
              <el-button size="small" @click="cancelReply">取消</el-button>
              <el-button size="small" type="primary" @click="submitReply(comment)" :loading="replying">
                回复
              </el-button>
            </div>
          </div>

          <!-- 子评论 -->
          <div v-if="comment.replyCount > 0" class="replies">
            <div v-if="!comment.showReplies" class="load-replies">
              <el-button text size="small" @click="loadReplies(comment)">
                展开 {{ comment.replyCount }} 条回复
                <el-icon><ArrowDown /></el-icon>
              </el-button>
            </div>

            <div v-else class="reply-list">
              <div
                v-for="reply in comment.replies"
                :key="reply.id"
                class="reply-item"
              >
                <div class="reply-avatar">
                  <el-avatar :size="32" :src="reply.userAvatar">
                    <el-icon><User /></el-icon>
                  </el-avatar>
                </div>
                <div class="reply-content">
                  <div class="reply-header">
                    <span class="username">{{ reply.userName }}</span>
                    <span class="reply-time">{{ formatTime(reply.createTime) }}</span>
                  </div>
                  <div class="reply-text">
                    <p v-if="reply.replyToUserName" class="reply-to">
                      回复 <span class="reply-user">@{{ reply.replyToUserName }}</span>:
                    </p>
                    <p>{{ reply.content }}</p>
                  </div>
                  <div class="reply-footer">
                    <el-button
                      text
                      size="small"
                      :class="{ 'liked': reply.hasLiked }"
                      @click="likeComment(reply)"
                    >
                      <el-icon><CaretTop /></el-icon>
                      {{ reply.likeCount || 0 }}
                    </el-button>
                    <el-button text size="small" @click="replyToComment(reply, comment)">
                      <el-icon><ChatRound /></el-icon>
                      回复
                    </el-button>
                  </div>
                </div>
              </div>

              <div class="collapse-replies">
                <el-button text size="small" @click="comment.showReplies = false">
                  收起回复
                  <el-icon><ArrowUp /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="comment-pagination" v-if="pagination.total > 0">
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next"
        @size-change="loadComments"
        @current-change="loadComments"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useUserStore } from '@/store/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

const props = defineProps({
  targetType: {
    type: String,
    required: true
  },
  targetId: {
    type: Number,
    required: true
  }
})

const userStore = useUserStore()

const loading = ref(false)
const submitting = ref(false)
const replying = ref(false)
const showSubmitButton = ref(false)
const newComment = ref('')
const replyContent = ref('')
const replyingTo = ref(null)
const sortBy = ref('time')
const commentCount = ref(0)

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const comments = ref([])

const canManageComment = (comment) => {
  return userStore.userInfo?.roleIds?.includes('1') || userStore.userInfo?.roleIds?.includes('2')
}

const canDeleteComment = (comment) => {
  return comment.userId === userStore.userInfo?.id || canManageComment(comment)
}

const loadComments = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      sortBy: sortBy.value
    }

    const response = await request.get(`/comments/${props.targetType}/${props.targetId}`, { params })
    comments.value = response.data.records || response.data
    pagination.total = response.data.total || 0
    
    // 加载评论数量
    await loadCommentCount()
  } catch (error) {
    ElMessage.error('加载评论失败')
  } finally {
    loading.value = false
  }
}

const loadCommentCount = async () => {
  try {
    const response = await request.get(`/comments/${props.targetType}/${props.targetId}/count`)
    commentCount.value = response.data || 0
  } catch (error) {
    console.error('加载评论数量失败', error)
  }
}

const submitComment = async () => {
  if (!newComment.value.trim()) {
    ElMessage.warning('请输入评论内容')
    return
  }

  submitting.value = true
  try {
    await request.post('/comments', {
      targetType: props.targetType,
      targetId: props.targetId,
      content: newComment.value.trim()
    })

    ElMessage.success('评论发表成功')
    newComment.value = ''
    showSubmitButton.value = false
    pagination.current = 1
    loadComments()
  } catch (error) {
    ElMessage.error('评论发表失败')
  } finally {
    submitting.value = false
  }
}

const cancelComment = () => {
  newComment.value = ''
  showSubmitButton.value = false
}

const replyToComment = (comment, parentComment = null) => {
  replyingTo.value = comment.id
  replyContent.value = ''
}

const submitReply = async (comment) => {
  if (!replyContent.value.trim()) {
    ElMessage.warning('请输入回复内容')
    return
  }

  replying.value = true
  try {
    await request.post('/comments', {
      targetType: props.targetType,
      targetId: props.targetId,
      content: replyContent.value.trim(),
      parentId: comment.parentId || comment.id,
      replyToUserId: comment.userId
    })

    ElMessage.success('回复发表成功')
    cancelReply()
    loadComments()
  } catch (error) {
    ElMessage.error('回复发表失败')
  } finally {
    replying.value = false
  }
}

const cancelReply = () => {
  replyingTo.value = null
  replyContent.value = ''
}

const likeComment = async (comment) => {
  try {
    await request.post(`/comments/${comment.id}/like`)
    
    // 更新本地状态
    comment.hasLiked = !comment.hasLiked
    comment.likeCount += comment.hasLiked ? 1 : -1
    
    ElMessage.success(comment.hasLiked ? '点赞成功' : '取消点赞')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const loadReplies = async (comment) => {
  try {
    const response = await request.get(`/comments/${comment.id}/replies`)
    comment.replies = response.data
    comment.showReplies = true
  } catch (error) {
    ElMessage.error('加载回复失败')
  }
}

const handleCommentAction = async ({ action, comment }) => {
  switch (action) {
    case 'pin':
      await pinComment(comment)
      break
    case 'delete':
      await deleteComment(comment)
      break
    case 'report':
      await reportComment(comment)
      break
  }
}

const pinComment = async (comment) => {
  try {
    await request.post(`/comments/${comment.id}/pin`, {
      isPinned: !comment.isPinned
    })
    
    comment.isPinned = !comment.isPinned
    ElMessage.success(comment.isPinned ? '置顶成功' : '取消置顶成功')
    loadComments()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const deleteComment = async (comment) => {
  try {
    await ElMessageBox.confirm('确定要删除这条评论吗？', '确认删除', {
      type: 'warning'
    })
    
    await request.delete(`/comments/${comment.id}`)
    ElMessage.success('删除成功')
    loadComments()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const reportComment = async (comment) => {
  try {
    const { value: reason } = await ElMessageBox.prompt('请输入举报原因', '举报评论', {
      inputValidator: (value) => {
        if (!value || value.trim() === '') {
          return '请输入举报原因'
        }
        return true
      }
    })
    
    await request.post(`/comments/${comment.id}/report`, {
      reason: reason
    })
    
    ElMessage.success('举报成功，我们会尽快处理')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('举报失败')
    }
  }
}

const formatTime = (time) => {
  if (!time) return ''
  try {
    return formatDistanceToNow(new Date(time), {
      addSuffix: true,
      locale: zhCN
    })
  } catch (error) {
    return time
  }
}

onMounted(() => {
  loadComments()
})
</script>

<style scoped>
.comment-section {
  margin-top: 30px;
  padding: 20px 0;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.comment-header h3 {
  margin: 0;
  color: #333;
}

.comment-form {
  margin-bottom: 30px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
}

.form-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.username {
  font-weight: 500;
  color: #333;
}

.form-actions {
  margin-top: 15px;
  text-align: right;
}

.form-actions .el-button {
  margin-left: 10px;
}

.login-prompt {
  margin-bottom: 30px;
}

.comment-list {
  min-height: 200px;
}

.empty-comments {
  text-align: center;
  padding: 60px 0;
}

.comment-item {
  display: flex;
  gap: 15px;
  padding: 20px 0;
  border-bottom: 1px solid #f0f0f0;
}

.comment-item.pinned {
  background: #fff7e6;
  border-left: 3px solid #fa8c16;
  padding-left: 17px;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.username {
  font-weight: 500;
  color: #333;
}

.comment-time {
  color: #999;
  font-size: 12px;
}

.comment-text {
  margin-bottom: 15px;
  line-height: 1.6;
  color: #555;
}

.comment-text p {
  margin: 0;
}

.reply-to {
  color: #1890ff;
  margin-bottom: 5px;
}

.reply-user {
  font-weight: 500;
}

.comment-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comment-stats {
  display: flex;
  gap: 15px;
}

.comment-stats .el-button.liked {
  color: #1890ff;
}

.reply-form {
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.reply-actions {
  margin-top: 10px;
  text-align: right;
}

.reply-actions .el-button {
  margin-left: 10px;
}

.replies {
  margin-top: 15px;
  padding-left: 20px;
  border-left: 2px solid #f0f0f0;
}

.load-replies {
  padding: 10px 0;
}

.reply-list {
  margin-top: 10px;
}

.reply-item {
  display: flex;
  gap: 10px;
  padding: 15px 0;
  border-bottom: 1px solid #f5f5f5;
}

.reply-item:last-child {
  border-bottom: none;
}

.reply-content {
  flex: 1;
}

.reply-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.reply-time {
  color: #999;
  font-size: 12px;
}

.reply-text {
  margin-bottom: 10px;
  line-height: 1.5;
  color: #555;
  font-size: 14px;
}

.reply-footer {
  display: flex;
  gap: 15px;
}

.reply-footer .el-button.liked {
  color: #1890ff;
}

.collapse-replies {
  padding: 10px 0;
  text-align: center;
}

.comment-pagination {
  margin-top: 30px;
  text-align: center;
}

@media (max-width: 768px) {
  .comment-section {
    padding: 10px 0;
  }
  
  .comment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .comment-item {
    gap: 10px;
  }
  
  .comment-content {
    overflow-x: hidden;
  }
  
  .form-actions {
    text-align: left;
  }
  
  .replies {
    padding-left: 10px;
  }
}
</style>