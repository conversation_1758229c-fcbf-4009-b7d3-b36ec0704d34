package com.aquaculture.management.enums;

public enum ResourceType {
    
    TRADITIONAL_CRAFT("传统工艺", "traditional_craft"),
    FOLK_SONG("民歌", "folk_song"),
    DANCE("舞蹈", "dance"),
    FESTIVAL("节庆", "festival"),
    STORY("故事传说", "story"),
    CLOTHING("服饰", "clothing"),
    ARCHITECTURE("建筑", "architecture"),
    FOOD("饮食文化", "food"),
    LANGUAGE("语言文字", "language"),
    CUSTOM("风俗习惯", "custom"),
    RELIGION("宗教信仰", "religion"),
    ART("艺术作品", "art");

    private final String name;
    private final String code;

    ResourceType(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}