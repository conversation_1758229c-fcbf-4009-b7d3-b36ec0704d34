package com.aquaculture.management.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "user_profile")
@TableName("user_profile")
@EqualsAndHashCode(callSuper = true)
public class UserProfile extends BaseEntity {

    private Long userId;

    private String nickname;

    private String bio;

    private String avatar;

    private LocalDate birthday;

    private String gender;

    private String location;

    private String occupation;

    private String interests;

    private String website;

    private String socialMedia;

    private Integer followersCount;

    private Integer followingCount;

    private Integer resourceCount;

    private Integer favoriteCount;

    private String preferences;

    private Integer privacyLevel;
}