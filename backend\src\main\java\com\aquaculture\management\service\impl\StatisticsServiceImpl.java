package com.aquaculture.management.service.impl;

import com.aquaculture.management.entity.Statistics;
import com.aquaculture.management.mapper.StatisticsMapper;
import com.aquaculture.management.service.StatisticsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class StatisticsServiceImpl extends ServiceImpl<StatisticsMapper, Statistics> implements StatisticsService {

    @Override
    public Map<String, Object> getDashboardStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        statistics.put("totalUsers", this.baseMapper.getTotalUsers());
        statistics.put("totalResources", this.baseMapper.getTotalResources());
        statistics.put("totalDocuments", this.baseMapper.getTotalDocuments());
        statistics.put("totalActivities", this.baseMapper.getTotalActivities());
        
        return statistics;
    }

    @Override
    public Map<String, Object> getOverviewStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        statistics.put("totalUsers", this.baseMapper.getTotalUsers());
        statistics.put("totalResources", this.baseMapper.getTotalResources());
        statistics.put("totalDocuments", this.baseMapper.getTotalDocuments());
        statistics.put("totalActivities", this.baseMapper.getTotalActivities());
        statistics.put("totalResourceViews", this.baseMapper.getTotalResourceViews());
        statistics.put("totalResourceDownloads", this.baseMapper.getTotalResourceDownloads());
        statistics.put("totalDocumentViews", this.baseMapper.getTotalDocumentViews());
        statistics.put("totalDocumentDownloads", this.baseMapper.getTotalDocumentDownloads());
        
        return statistics;
    }

    @Override
    public void generateDailyStatistics() {
        LocalDate today = LocalDate.now();
        
        Statistics userStats = new Statistics();
        userStats.setStatisticsDate(today);
        userStats.setType("用户统计");
        userStats.setTotalCount(this.baseMapper.getTotalUsers().intValue());
        this.save(userStats);
        
        Statistics resourceStats = new Statistics();
        resourceStats.setStatisticsDate(today);
        resourceStats.setType("资源统计");
        resourceStats.setTotalCount(this.baseMapper.getTotalResources().intValue());
        resourceStats.setViewCount(this.baseMapper.getTotalResourceViews().intValue());
        resourceStats.setDownloadCount(this.baseMapper.getTotalResourceDownloads().intValue());
        this.save(resourceStats);
        
        Statistics documentStats = new Statistics();
        documentStats.setStatisticsDate(today);
        documentStats.setType("文档统计");
        documentStats.setTotalCount(this.baseMapper.getTotalDocuments().intValue());
        documentStats.setViewCount(this.baseMapper.getTotalDocumentViews().intValue());
        documentStats.setDownloadCount(this.baseMapper.getTotalDocumentDownloads().intValue());
        this.save(documentStats);
        
        Statistics activityStats = new Statistics();
        activityStats.setStatisticsDate(today);
        activityStats.setType("活动统计");
        activityStats.setTotalCount(this.baseMapper.getTotalActivities().intValue());
        this.save(activityStats);
    }
}