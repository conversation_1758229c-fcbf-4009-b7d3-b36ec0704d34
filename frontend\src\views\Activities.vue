<template>
  <div class="activities">
    <div class="page-header">
      <h2>活动管理</h2>
      <p>管理水族文化相关活动和事件</p>
    </div>

    <div class="page-content">
      <div class="toolbar">
        <el-row :gutter="20" style="margin-bottom: 20px;">
          <el-col :span="6">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索活动名称、描述或地点"
              prefix-icon="Search"
              @input="handleSearch"
            />
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.type" placeholder="活动类型" clearable @change="handleSearch">
              <el-option label="文化节庆" value="文化节庆" />
              <el-option label="技能培训" value="技能培训" />
              <el-option label="文艺演出" value="文艺演出" />
              <el-option label="比赛活动" value="比赛活动" />
              <el-option label="学术考察" value="学术考察" />
              <el-option label="展览展示" value="展览展示" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.status" placeholder="活动状态" clearable @change="handleSearch">
              <el-option label="进行中" value="1" />
              <el-option label="已结束" value="2" />
              <el-option label="已取消" value="0" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" icon="Plus" @click="showAddDialog = true">
              创建活动
            </el-button>
          </el-col>
        </el-row>
      </div>

      <el-table :data="activityList" v-loading="loading" style="width: 100%">
        <el-table-column prop="name" label="活动名称" width="250" show-overflow-tooltip />
        <el-table-column prop="type" label="类型" width="120" />
        <el-table-column prop="location" label="地点" width="150" show-overflow-tooltip />
        <el-table-column prop="organizer" label="主办方" width="120" show-overflow-tooltip />
        <el-table-column label="参与人数" width="120">
          <template #default="scope">
            {{ scope.row.currentParticipants }} / {{ scope.row.maxParticipants }}
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="开始时间" width="180" />
        <el-table-column prop="endTime" label="结束时间" width="180" />
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" @click="viewActivity(scope.row)">
              查看
            </el-button>
            <el-button 
              size="small" 
              type="success" 
              @click="participateActivity(scope.row)"
              :disabled="scope.row.status !== 1 || scope.row.currentParticipants >= scope.row.maxParticipants"
            >
              参与
            </el-button>
            <el-button size="small" type="warning" @click="editActivity(scope.row)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="deleteActivity(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadActivities"
          @current-change="loadActivities"
        />
      </div>
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="isEdit ? '编辑活动' : '创建活动'"
      width="900px"
      @close="resetForm"
    >
      <el-form
        ref="activityFormRef"
        :model="activityForm"
        :rules="activityRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="活动名称" prop="name">
              <el-input v-model="activityForm.name" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="活动类型" prop="type">
              <el-select v-model="activityForm.type" style="width: 100%">
                <el-option label="文化节庆" value="文化节庆" />
                <el-option label="技能培训" value="技能培训" />
                <el-option label="文艺演出" value="文艺演出" />
                <el-option label="比赛活动" value="比赛活动" />
                <el-option label="学术考察" value="学术考察" />
                <el-option label="展览展示" value="展览展示" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="活动地点" prop="location">
              <el-input v-model="activityForm.location" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="主办方" prop="organizer">
              <el-input v-model="activityForm.organizer" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="activityForm.startTime"
                type="datetime"
                placeholder="选择开始时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="activityForm.endTime"
                type="datetime"
                placeholder="选择结束时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="最大人数" prop="maxParticipants">
              <el-input-number
                v-model="activityForm.maxParticipants"
                :min="1"
                :max="10000"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系人" prop="contact">
              <el-input v-model="activityForm.contact" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="activityForm.phone" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系邮箱" prop="email">
              <el-input v-model="activityForm.email" type="email" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="费用说明" prop="fee">
              <el-input v-model="activityForm.fee" placeholder="如：免费、100元/人" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="活动描述" prop="description">
          <el-input v-model="activityForm.description" type="textarea" :rows="4" />
        </el-form-item>

        <el-form-item label="参与要求" prop="requirements">
          <el-input v-model="activityForm.requirements" type="textarea" :rows="3" />
        </el-form-item>

        <el-form-item label="所需材料" prop="materials">
          <el-input v-model="activityForm.materials" type="textarea" :rows="3" />
        </el-form-item>

        <el-form-item label="标签" prop="tags">
          <el-input v-model="activityForm.tags" placeholder="用逗号分隔多个标签" />
        </el-form-item>

        <el-form-item label="备注" prop="notes">
          <el-input v-model="activityForm.notes" type="textarea" :rows="2" />
        </el-form-item>

        <el-form-item label="封面图片">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :on-change="handleFileChange"
            :limit="1"
            accept="image/*"
            list-type="picture-card"
          >
            <el-icon><Plus /></el-icon>
            <template #tip>
              <div class="el-upload__tip">
                支持 JPG、PNG 格式，大小不超过10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="saveActivity">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 查看活动详情对话框 -->
    <el-dialog v-model="showViewDialog" title="活动详情" width="1000px">
      <div v-if="currentActivity" class="activity-detail">
        <el-row :gutter="20">
          <el-col :span="16">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="活动名称" :span="2">{{ currentActivity.name }}</el-descriptions-item>
              <el-descriptions-item label="活动类型">{{ currentActivity.type }}</el-descriptions-item>
              <el-descriptions-item label="活动状态">
                <el-tag :type="getStatusType(currentActivity.status)">
                  {{ getStatusText(currentActivity.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="活动地点" :span="2">{{ currentActivity.location }}</el-descriptions-item>
              <el-descriptions-item label="主办方">{{ currentActivity.organizer }}</el-descriptions-item>
              <el-descriptions-item label="费用说明">{{ currentActivity.fee || '未说明' }}</el-descriptions-item>
              <el-descriptions-item label="开始时间">{{ currentActivity.startTime }}</el-descriptions-item>
              <el-descriptions-item label="结束时间">{{ currentActivity.endTime }}</el-descriptions-item>
              <el-descriptions-item label="最大人数">{{ currentActivity.maxParticipants }}</el-descriptions-item>
              <el-descriptions-item label="当前人数">{{ currentActivity.currentParticipants }}</el-descriptions-item>
              <el-descriptions-item label="联系人">{{ currentActivity.contact }}</el-descriptions-item>
              <el-descriptions-item label="联系电话">{{ currentActivity.phone }}</el-descriptions-item>
              <el-descriptions-item label="联系邮箱" :span="2">{{ currentActivity.email }}</el-descriptions-item>
            </el-descriptions>

            <div style="margin-top: 20px;">
              <h4>活动描述</h4>
              <p>{{ currentActivity.description || '暂无描述' }}</p>
            </div>

            <div style="margin-top: 20px;">
              <h4>参与要求</h4>
              <p>{{ currentActivity.requirements || '无特殊要求' }}</p>
            </div>

            <div style="margin-top: 20px;">
              <h4>所需材料</h4>
              <p>{{ currentActivity.materials || '无需准备材料' }}</p>
            </div>

            <div style="margin-top: 20px;">
              <h4>标签</h4>
              <el-tag v-for="tag in getTags(currentActivity.tags)" :key="tag" style="margin-right: 5px;">
                {{ tag }}
              </el-tag>
            </div>

            <div v-if="currentActivity.notes" style="margin-top: 20px;">
              <h4>备注</h4>
              <p>{{ currentActivity.notes }}</p>
            </div>
          </el-col>
          <el-col :span="8">
            <div v-if="currentActivity.coverImage" class="cover-image">
              <h4>封面图片</h4>
              <el-image
                :src="currentActivity.coverImage"
                fit="cover"
                style="width: 100%; height: 200px; border-radius: 8px;"
              />
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'

const loading = ref(false)
const submitLoading = ref(false)
const showAddDialog = ref(false)
const showViewDialog = ref(false)
const isEdit = ref(false)
const activityFormRef = ref()
const uploadRef = ref()
const currentActivity = ref(null)

const searchForm = reactive({
  keyword: '',
  type: '',
  status: ''
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const activityList = ref([])

const activityForm = reactive({
  id: null,
  name: '',
  type: '',
  description: '',
  location: '',
  startTime: '',
  endTime: '',
  organizer: '',
  contact: '',
  phone: '',
  email: '',
  maxParticipants: 50,
  requirements: '',
  materials: '',
  coverImage: '',
  tags: '',
  fee: '',
  notes: ''
})

const activityRules = {
  name: [{ required: true, message: '请输入活动名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择活动类型', trigger: 'change' }],
  location: [{ required: true, message: '请输入活动地点', trigger: 'blur' }],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
  organizer: [{ required: true, message: '请输入主办方', trigger: 'blur' }],
  contact: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }]
}

const loadActivities = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    const response = await request.get('/activities', { params })
    activityList.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载活动列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadActivities()
}

const viewActivity = (activity) => {
  currentActivity.value = activity
  showViewDialog.value = true
}

const participateActivity = async (activity) => {
  try {
    await ElMessageBox.confirm('确定要参与这个活动吗？', '确认参与', {
      type: 'question'
    })
    
    await request.post(`/activities/${activity.id}/participate`)
    ElMessage.success('参与成功')
    activity.currentParticipants++
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('参与失败')
    }
  }
}

const editActivity = (activity) => {
  isEdit.value = true
  Object.assign(activityForm, activity)
  showAddDialog.value = true
}

const deleteActivity = async (activity) => {
  try {
    await ElMessageBox.confirm(`确定要删除活动"${activity.name}"吗？`, '确认删除', {
      type: 'warning'
    })
    
    await request.delete(`/activities/${activity.id}`)
    ElMessage.success('删除成功')
    loadActivities()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleFileChange = (file) => {
  // 处理封面图片变更
}

const saveActivity = async () => {
  if (!activityFormRef.value) return
  
  await activityFormRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      try {
        // 如果有封面图片需要上传
        if (uploadRef.value && uploadRef.value.uploadFiles.length > 0) {
          const file = uploadRef.value.uploadFiles[0].raw
          const formData = new FormData()
          formData.append('file', file)
          formData.append('category', 'activities')
          
          const uploadResponse = await request.post('/files/upload', formData, {
            headers: { 'Content-Type': 'multipart/form-data' }
          })
          
          activityForm.coverImage = uploadResponse.data.url
        }
        
        if (isEdit.value) {
          await request.put(`/activities/${activityForm.id}`, activityForm)
          ElMessage.success('更新成功')
        } else {
          await request.post('/activities', activityForm)
          ElMessage.success('创建成功')
        }
        
        showAddDialog.value = false
        resetForm()
        loadActivities()
      } catch (error) {
        ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      } finally {
        submitLoading.value = false
      }
    }
  })
}

const resetForm = () => {
  Object.assign(activityForm, {
    id: null,
    name: '',
    type: '',
    description: '',
    location: '',
    startTime: '',
    endTime: '',
    organizer: '',
    contact: '',
    phone: '',
    email: '',
    maxParticipants: 50,
    requirements: '',
    materials: '',
    coverImage: '',
    tags: '',
    fee: '',
    notes: ''
  })
  isEdit.value = false
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

const getStatusType = (status) => {
  const statusMap = {
    0: 'danger',  // 已取消
    1: 'success', // 进行中
    2: 'info'     // 已结束
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    0: '已取消',
    1: '进行中',
    2: '已结束'
  }
  return statusMap[status] || '未知'
}

const getTags = (tags) => {
  return tags ? tags.split(',').map(t => t.trim()).filter(t => t) : []
}

onMounted(() => {
  loadActivities()
})
</script>

<style scoped>
.activities {
  max-width: 1400px;
}

.toolbar {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.activity-detail {
  max-height: 700px;
  overflow-y: auto;
}

.cover-image {
  text-align: center;
}
</style>