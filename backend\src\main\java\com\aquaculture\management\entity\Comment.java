package com.aquaculture.management.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "comment")
@TableName("comment")
public class Comment extends BaseEntity {

    @Column(name = "content", nullable = false, columnDefinition = "TEXT")
    private String content;

    @Column(name = "target_type", nullable = false, length = 50)
    private String targetType; // cultural_resource, document, activity

    @Column(name = "target_id", nullable = false)
    private Long targetId;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "user_name", length = 50)
    private String userName;

    @Column(name = "user_avatar", length = 500)
    private String userAvatar;

    @Column(name = "parent_id")
    private Long parentId; // 父评论ID，用于回复

    @Column(name = "reply_to_user_id")
    private Long replyToUserId; // 回复的用户ID

    @Column(name = "reply_to_user_name", length = 50)
    private String replyToUserName; // 回复的用户名

    @Column(name = "like_count", nullable = false)
    private Integer likeCount = 0;

    @Column(name = "reply_count", nullable = false)
    private Integer replyCount = 0;

    @Column(name = "status", nullable = false)
    private Integer status = 1; // 0-隐藏，1-正常，2-待审核

    @Column(name = "ip_address", length = 64)
    private String ipAddress;

    @Column(name = "user_agent", length = 500)
    private String userAgent;

    @Column(name = "is_pinned")
    private Boolean isPinned = false; // 是否置顶

    @Column(name = "audit_status")
    private Integer auditStatus = 1; // 0-待审核，1-审核通过，2-审核拒绝

    @Column(name = "audit_time")
    private LocalDateTime auditTime;

    @Column(name = "audit_reason", columnDefinition = "TEXT")
    private String auditReason;

    // 非数据库字段，用于前端显示
    @javax.persistence.Transient
    @com.baomidou.mybatisplus.annotation.TableField(exist = false)
    private Boolean hasLiked = false; // 当前用户是否已点赞
}