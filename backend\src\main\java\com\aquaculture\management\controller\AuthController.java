package com.aquaculture.management.controller;

import com.aquaculture.management.dto.LoginDTO;
import com.aquaculture.management.dto.RegisterDTO;
import com.aquaculture.management.entity.User;
import com.aquaculture.management.service.UserService;
import com.aquaculture.management.utils.PasswordUtil;
import com.aquaculture.management.utils.Result;
import com.aquaculture.management.vo.LoginVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Tag(name = "认证管理")
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@CrossOrigin
public class AuthController {

    private final UserService userService;

    @Operation(summary = "用户登录")
    @PostMapping("/login")
    public Result<LoginVO> login(@RequestBody @Validated LoginDTO loginDTO) {
        try {
            LoginVO loginVO = userService.login(loginDTO);
            return Result.success("登录成功", loginVO);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "用户注册")
    @PostMapping("/register")
    public Result<Void> register(@RequestBody @Validated RegisterDTO registerDTO) {
        try {
            // 验证两次密码是否一致
            if (!registerDTO.getPassword().equals(registerDTO.getConfirmPassword())) {
                return Result.error("两次输入的密码不一致");
            }

            // 验证密码强度
            PasswordUtil.PasswordValidationResult validationResult = PasswordUtil.validatePassword(registerDTO.getPassword());
            if (!validationResult.isValid()) {
                return Result.error("密码不符合要求：" + validationResult.getErrorMessage());
            }

            // 验证用户名格式
            if (!PasswordUtil.isValidUsername(registerDTO.getUsername())) {
                return Result.error("用户名格式不正确：" + PasswordUtil.getUsernameFormatDescription());
            }

            // 验证手机号格式
            if (!PasswordUtil.isValidPhone(registerDTO.getPhone())) {
                return Result.error("手机号格式不正确");
            }

            // 检查用户名是否已存在
            if (userService.getUserByUsername(registerDTO.getUsername()) != null) {
                return Result.error("用户名已存在");
            }

            // 检查邮箱是否已存在
            if (registerDTO.getEmail() != null && userService.getUserByEmail(registerDTO.getEmail()) != null) {
                return Result.error("邮箱已被使用");
            }

            // 创建用户
            User user = new User();
            user.setUsername(registerDTO.getUsername());
            user.setPassword(registerDTO.getPassword()); // 密码将在service层进行加密
            user.setRealName(registerDTO.getRealName());
            user.setEmail(registerDTO.getEmail());
            user.setPhone(registerDTO.getPhone());
            user.setRoleIds("4"); // 默认普通用户角色

            boolean success = userService.createUser(user);
            if (success) {
                return Result.success("注册成功");
            } else {
                return Result.error("注册失败");
            }
        } catch (Exception e) {
            return Result.error("注册失败：" + e.getMessage());
        }
    }

    @Operation(summary = "用户登出")
    @PostMapping("/logout")
    public Result<Void> logout() {
        return Result.success("退出成功");
    }

    @Operation(summary = "验证密码强度")
    @PostMapping("/validate-password")
    public Result<PasswordUtil.PasswordValidationResult> validatePassword(@RequestParam String password) {
        PasswordUtil.PasswordValidationResult result = PasswordUtil.validatePassword(password);
        return Result.success(result);
    }

    @Operation(summary = "检查用户名是否可用")
    @GetMapping("/check-username")
    public Result<Boolean> checkUsername(@RequestParam String username) {
        try {
            if (!PasswordUtil.isValidUsername(username)) {
                return Result.error("用户名格式不正确");
            }
            
            boolean available = userService.getUserByUsername(username) == null;
            return Result.success(available);
        } catch (Exception e) {
            return Result.error("检查用户名失败");
        }
    }

    @Operation(summary = "检查邮箱是否可用")
    @GetMapping("/check-email")
    public Result<Boolean> checkEmail(@RequestParam String email) {
        try {
            boolean available = userService.getUserByEmail(email) == null;
            return Result.success(available);
        } catch (Exception e) {
            return Result.error("检查邮箱失败");
        }
    }
}