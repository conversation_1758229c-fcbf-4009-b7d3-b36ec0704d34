package com.aquaculture.management.mapper;

import com.aquaculture.management.entity.CulturalResource;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface CulturalResourceMapper extends BaseMapper<CulturalResource> {

    @Select("SELECT type, COUNT(*) as count FROM cultural_resource WHERE deleted = 0 GROUP BY type")
    List<Map<String, Object>> getResourceTypeStatistics();

    @Select("SELECT region, COUNT(*) as count FROM cultural_resource WHERE deleted = 0 AND region IS NOT NULL GROUP BY region")
    List<Map<String, Object>> getResourceRegionStatistics();
}