package com.aquaculture.management.service;

import com.aquaculture.management.dto.LoginDTO;
import com.aquaculture.management.entity.User;
import com.aquaculture.management.vo.LoginVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

public interface UserService extends IService<User> {

    LoginVO login(LoginDTO loginDTO);

    User getUserByUsername(String username);

    User getUserByEmail(String email);

    IPage<User> getUsers(int current, int size, String keyword);

    boolean createUser(User user);

    boolean updateUser(User user);

    boolean deleteUser(Long id);

    boolean resetPassword(Long id, String newPassword);
}