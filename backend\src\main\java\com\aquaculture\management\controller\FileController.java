package com.aquaculture.management.controller;

import com.aquaculture.management.utils.FileUtil;
import com.aquaculture.management.utils.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/files")
@RequiredArgsConstructor
@CrossOrigin
public class FileController {

    private final FileUtil fileUtil;

    @PostMapping("/upload")
    public Result<Map<String, Object>> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "category", defaultValue = "general") String category) {
        
        try {
            String fileUrl = fileUtil.uploadFile(file, category);
            
            Map<String, Object> result = new HashMap<>();
            result.put("url", fileUrl);
            result.put("name", file.getOriginalFilename());
            result.put("size", file.getSize());
            result.put("type", fileUtil.getFileType(file.getOriginalFilename()));
            
            // 如果是图片，生成缩略图
            String thumbnail = fileUtil.generateThumbnail(fileUrl);
            if (thumbnail != null) {
                result.put("thumbnail", thumbnail);
            }
            
            log.info("文件上传成功: {}", fileUrl);
            return Result.success("上传成功", result);
            
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return Result.error("上传失败: " + e.getMessage());
        }
    }

    @PostMapping("/upload-multiple")
    public Result<Map<String, Object>> uploadMultipleFiles(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam(value = "category", defaultValue = "general") String category) {
        
        try {
            Map<String, Object> result = new HashMap<>();
            Map<String, Object> successFiles = new HashMap<>();
            Map<String, String> failedFiles = new HashMap<>();
            
            for (MultipartFile file : files) {
                try {
                    String fileUrl = fileUtil.uploadFile(file, category);
                    
                    Map<String, Object> fileInfo = new HashMap<>();
                    fileInfo.put("url", fileUrl);
                    fileInfo.put("size", file.getSize());
                    fileInfo.put("type", fileUtil.getFileType(file.getOriginalFilename()));
                    
                    String thumbnail = fileUtil.generateThumbnail(fileUrl);
                    if (thumbnail != null) {
                        fileInfo.put("thumbnail", thumbnail);
                    }
                    
                    successFiles.put(file.getOriginalFilename(), fileInfo);
                    
                } catch (Exception e) {
                    failedFiles.put(file.getOriginalFilename(), e.getMessage());
                    log.error("文件上传失败: {}", file.getOriginalFilename(), e);
                }
            }
            
            result.put("success", successFiles);
            result.put("failed", failedFiles);
            result.put("successCount", successFiles.size());
            result.put("failedCount", failedFiles.size());
            
            return Result.success("批量上传完成", result);
            
        } catch (Exception e) {
            log.error("批量文件上传失败", e);
            return Result.error("批量上传失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/delete")
    public Result<Void> deleteFile(@RequestParam("url") String fileUrl) {
        try {
            boolean deleted = fileUtil.deleteFile(fileUrl);
            if (deleted) {
                log.info("文件删除成功: {}", fileUrl);
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败，文件不存在或无法删除");
            }
        } catch (Exception e) {
            log.error("文件删除失败", e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    @GetMapping("/download")
    public ResponseEntity<Resource> downloadFile(
            @RequestParam("url") String fileUrl,
            HttpServletRequest request) throws IOException {
        
        try {
            // 验证文件URL格式
            if (!fileUrl.contains("/api/files/")) {
                return ResponseEntity.badRequest().build();
            }
            
            // 提取相对路径
            String relativePath = fileUrl.substring(fileUrl.indexOf("/api/files/") + "/api/files/".length());
            Path filePath = Paths.get("./uploads/", relativePath);
            
            if (!Files.exists(filePath)) {
                return ResponseEntity.notFound().build();
            }
            
            Resource resource = new FileSystemResource(filePath);
            
            // 获取文件名
            String filename = filePath.getFileName().toString();
            String originalFilename = request.getParameter("filename");
            if (originalFilename != null && !originalFilename.isEmpty()) {
                filename = originalFilename;
            }
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, 
                    "attachment; filename=\"" + URLEncoder.encode(filename, StandardCharsets.UTF_8) + "\"");
            
            // 确定Content-Type
            String contentType = Files.probeContentType(filePath);
            if (contentType == null) {
                contentType = "application/octet-stream";
            }
            
            log.info("文件下载: {}", fileUrl);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentType(MediaType.parseMediaType(contentType))
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("文件下载失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/info")
    public Result<Map<String, Object>> getFileInfo(@RequestParam("url") String fileUrl) {
        try {
            Map<String, Object> info = new HashMap<>();
            info.put("url", fileUrl);
            info.put("size", fileUtil.getFileSize(fileUrl));
            info.put("type", fileUtil.getFileType(fileUrl));
            
            // 检查文件是否存在
            String relativePath = fileUrl.substring(fileUrl.indexOf("/api/files/") + "/api/files/".length());
            Path filePath = Paths.get("./uploads/", relativePath);
            info.put("exists", Files.exists(filePath));
            
            if (Files.exists(filePath)) {
                info.put("lastModified", Files.getLastModifiedTime(filePath).toMillis());
            }
            
            return Result.success(info);
        } catch (Exception e) {
            log.error("获取文件信息失败", e);
            return Result.error("获取文件信息失败: " + e.getMessage());
        }
    }
}