package com.aquaculture.management.service.impl;

import com.aquaculture.management.entity.CulturalResource;
import com.aquaculture.management.mapper.CulturalResourceMapper;
import com.aquaculture.management.service.CulturalResourceService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class CulturalResourceServiceImpl extends ServiceImpl<CulturalResourceMapper, CulturalResource> implements CulturalResourceService {

    @Override
    public IPage<CulturalResource> getResources(int current, int size, String keyword, String type, String category, String region) {
        Page<CulturalResource> page = new Page<>(current, size);
        LambdaQueryWrapper<CulturalResource> wrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(keyword)) {
            wrapper.like(CulturalResource::getName, keyword)
                   .or()
                   .like(CulturalResource::getDescription, keyword)
                   .or()
                   .like(CulturalResource::getKeywords, keyword);
        }

        if (StringUtils.hasText(type)) {
            wrapper.eq(CulturalResource::getType, type);
        }

        if (StringUtils.hasText(category)) {
            wrapper.eq(CulturalResource::getCategory, category);
        }

        if (StringUtils.hasText(region)) {
            wrapper.eq(CulturalResource::getRegion, region);
        }

        wrapper.eq(CulturalResource::getStatus, 1)
               .orderByDesc(CulturalResource::getCreateTime);

        return this.page(page, wrapper);
    }

    @Override
    public boolean createResource(CulturalResource resource) {
        resource.setStatus(1);
        resource.setViewCount(0);
        resource.setDownloadCount(0);
        return this.save(resource);
    }

    @Override
    public boolean updateResource(CulturalResource resource) {
        CulturalResource existingResource = this.getById(resource.getId());
        if (existingResource == null) {
            throw new RuntimeException("资源不存在");
        }
        return this.updateById(resource);
    }

    @Override
    public boolean deleteResource(Long id) {
        return this.removeById(id);
    }

    @Override
    public void incrementViewCount(Long id) {
        CulturalResource resource = this.getById(id);
        if (resource != null) {
            resource.setViewCount(resource.getViewCount() + 1);
            this.updateById(resource);
        }
    }

    @Override
    public void incrementDownloadCount(Long id) {
        CulturalResource resource = this.getById(id);
        if (resource != null) {
            resource.setDownloadCount(resource.getDownloadCount() + 1);
            this.updateById(resource);
        }
    }

    @Override
    public List<CulturalResource> getRecommendedResources(int limit) {
        LambdaQueryWrapper<CulturalResource> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CulturalResource::getStatus, 1)
               .orderByDesc(CulturalResource::getViewCount)
               .orderByDesc(CulturalResource::getCreateTime)
               .last("LIMIT " + limit);
        return this.list(wrapper);
    }

    @Override
    public List<Map<String, Object>> getResourceTypeStatistics() {
        return this.baseMapper.getResourceTypeStatistics();
    }

    @Override
    public List<Map<String, Object>> getResourceRegionStatistics() {
        return this.baseMapper.getResourceRegionStatistics();
    }
}