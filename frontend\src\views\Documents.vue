<template>
  <div class="documents">
    <div class="page-header">
      <h2>文档资料管理</h2>
      <p>管理水族文化相关文档资料</p>
    </div>

    <div class="page-content">
      <div class="toolbar">
        <el-row :gutter="20" style="margin-bottom: 20px;">
          <el-col :span="6">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索文档标题或内容"
              prefix-icon="Search"
              @input="handleSearch"
            />
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.type" placeholder="文档类型" clearable @change="handleSearch">
              <el-option label="学术论文" value="学术论文" />
              <el-option label="调查报告" value="调查报告" />
              <el-option label="保护方案" value="保护方案" />
              <el-option label="史料文献" value="史料文献" />
              <el-option label="学术专著" value="学术专著" />
              <el-option label="技术手册" value="技术手册" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.category" placeholder="文档分类" clearable @change="handleSearch">
              <el-option label="文化研究" value="文化研究" />
              <el-option label="民俗研究" value="民俗研究" />
              <el-option label="非遗保护" value="非遗保护" />
              <el-option label="历史研究" value="历史研究" />
              <el-option label="语言研究" value="语言研究" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" icon="Plus" @click="showAddDialog = true">
              添加文档
            </el-button>
          </el-col>
        </el-row>
      </div>

      <el-table :data="documentList" v-loading="loading" style="width: 100%">
        <el-table-column prop="title" label="标题" width="300" show-overflow-tooltip />
        <el-table-column prop="type" label="类型" width="120" />
        <el-table-column prop="category" label="分类" width="120" />
        <el-table-column prop="author" label="作者" width="120" />
        <el-table-column prop="version" label="版本" width="80" />
        <el-table-column prop="viewCount" label="浏览次数" width="100" />
        <el-table-column prop="downloadCount" label="下载次数" width="100" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" @click="viewDocument(scope.row)">
              查看
            </el-button>
            <el-button size="small" type="success" @click="downloadDocument(scope.row)">
              下载
            </el-button>
            <el-button size="small" type="warning" @click="editDocument(scope.row)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="deleteDocument(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadDocuments"
          @current-change="loadDocuments"
        />
      </div>
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="isEdit ? '编辑文档' : '添加文档'"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="documentFormRef"
        :model="documentForm"
        :rules="documentRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="文档标题" prop="title">
              <el-input v-model="documentForm.title" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文档类型" prop="type">
              <el-select v-model="documentForm.type" style="width: 100%">
                <el-option label="学术论文" value="学术论文" />
                <el-option label="调查报告" value="调查报告" />
                <el-option label="保护方案" value="保护方案" />
                <el-option label="史料文献" value="史料文献" />
                <el-option label="学术专著" value="学术专著" />
                <el-option label="技术手册" value="技术手册" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="文档分类" prop="category">
              <el-input v-model="documentForm.category" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="作者" prop="author">
              <el-input v-model="documentForm.author" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="版本号" prop="version">
              <el-input v-model="documentForm.version" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="语言" prop="language">
              <el-select v-model="documentForm.language" style="width: 100%">
                <el-option label="中文" value="zh-CN" />
                <el-option label="英文" value="en-US" />
                <el-option label="水族语" value="shui" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="文档摘要" prop="summary">
          <el-input v-model="documentForm.summary" type="textarea" :rows="3" />
        </el-form-item>

        <el-form-item label="文档内容" prop="content">
          <el-input v-model="documentForm.content" type="textarea" :rows="8" />
        </el-form-item>

        <el-form-item label="关键词" prop="keywords">
          <el-input v-model="documentForm.keywords" placeholder="用逗号分隔多个关键词" />
        </el-form-item>

        <el-form-item label="标签" prop="tags">
          <el-input v-model="documentForm.tags" placeholder="用逗号分隔多个标签" />
        </el-form-item>

        <el-form-item label="文档文件">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :on-change="handleFileChange"
            :limit="1"
            accept=".pdf,.doc,.docx,.txt,.md"
          >
            <el-button slot="trigger" type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持 PDF、Word、文本文件，大小不超过50MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="saveDocument">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 查看文档对话框 -->
    <el-dialog v-model="showViewDialog" title="文档详情" width="900px">
      <div v-if="currentDocument" class="document-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="标题">{{ currentDocument.title }}</el-descriptions-item>
          <el-descriptions-item label="类型">{{ currentDocument.type }}</el-descriptions-item>
          <el-descriptions-item label="分类">{{ currentDocument.category }}</el-descriptions-item>
          <el-descriptions-item label="作者">{{ currentDocument.author }}</el-descriptions-item>
          <el-descriptions-item label="版本">{{ currentDocument.version }}</el-descriptions-item>
          <el-descriptions-item label="语言">{{ currentDocument.language }}</el-descriptions-item>
          <el-descriptions-item label="浏览次数">{{ currentDocument.viewCount }}</el-descriptions-item>
          <el-descriptions-item label="下载次数">{{ currentDocument.downloadCount }}</el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">{{ currentDocument.createTime }}</el-descriptions-item>
        </el-descriptions>
        
        <div style="margin-top: 20px;">
          <h4>文档摘要</h4>
          <p>{{ currentDocument.summary || '暂无摘要' }}</p>
        </div>

        <div style="margin-top: 20px;">
          <h4>文档内容</h4>
          <div class="document-content">
            {{ currentDocument.content || '暂无内容' }}
          </div>
        </div>

        <div style="margin-top: 20px;">
          <h4>关键词</h4>
          <el-tag v-for="keyword in getKeywords(currentDocument.keywords)" :key="keyword" style="margin-right: 5px;">
            {{ keyword }}
          </el-tag>
        </div>

        <div style="margin-top: 20px;">
          <h4>标签</h4>
          <el-tag v-for="tag in getTags(currentDocument.tags)" :key="tag" type="success" style="margin-right: 5px;">
            {{ tag }}
          </el-tag>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'

const loading = ref(false)
const submitLoading = ref(false)
const showAddDialog = ref(false)
const showViewDialog = ref(false)
const isEdit = ref(false)
const documentFormRef = ref()
const uploadRef = ref()
const currentDocument = ref(null)

const searchForm = reactive({
  keyword: '',
  type: '',
  category: ''
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const documentList = ref([])

const documentForm = reactive({
  id: null,
  title: '',
  type: '',
  category: '',
  content: '',
  summary: '',
  keywords: '',
  tags: '',
  author: '',
  version: '1.0',
  language: 'zh-CN',
  filePath: '',
  fileType: '',
  fileSize: null
})

const documentRules = {
  title: [{ required: true, message: '请输入文档标题', trigger: 'blur' }],
  type: [{ required: true, message: '请选择文档类型', trigger: 'change' }],
  author: [{ required: true, message: '请输入作者', trigger: 'blur' }]
}

const loadDocuments = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    const response = await request.get('/documents', { params })
    documentList.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载文档列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadDocuments()
}

const viewDocument = async (document) => {
  currentDocument.value = document
  showViewDialog.value = true
  
  // 增加浏览次数
  try {
    await request.get(`/documents/${document.id}`)
    document.viewCount++
  } catch (error) {
    console.error('增加浏览次数失败', error)
  }
}

const downloadDocument = async (document) => {
  try {
    await request.post(`/documents/${document.id}/download`)
    document.downloadCount++
    ElMessage.success('下载统计已更新')
    
    // 这里可以添加实际的文件下载逻辑
    if (document.filePath) {
      window.open(document.filePath, '_blank')
    } else {
      ElMessage.info('该文档暂无可下载文件')
    }
  } catch (error) {
    ElMessage.error('下载失败')
  }
}

const editDocument = (document) => {
  isEdit.value = true
  Object.assign(documentForm, document)
  showAddDialog.value = true
}

const deleteDocument = async (document) => {
  try {
    await ElMessageBox.confirm(`确定要删除文档"${document.title}"吗？`, '确认删除', {
      type: 'warning'
    })
    
    await request.delete(`/documents/${document.id}`)
    ElMessage.success('删除成功')
    loadDocuments()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleFileChange = (file) => {
  documentForm.fileType = file.name.split('.').pop()
  documentForm.fileSize = file.size
}

const saveDocument = async () => {
  if (!documentFormRef.value) return
  
  await documentFormRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      try {
        // 如果有文件需要上传
        if (uploadRef.value && uploadRef.value.uploadFiles.length > 0) {
          const file = uploadRef.value.uploadFiles[0].raw
          const formData = new FormData()
          formData.append('file', file)
          formData.append('category', 'documents')
          
          const uploadResponse = await request.post('/files/upload', formData, {
            headers: { 'Content-Type': 'multipart/form-data' }
          })
          
          documentForm.filePath = uploadResponse.data.url
          documentForm.fileType = uploadResponse.data.type
          documentForm.fileSize = uploadResponse.data.size
        }
        
        if (isEdit.value) {
          await request.put(`/documents/${documentForm.id}`, documentForm)
          ElMessage.success('更新成功')
        } else {
          await request.post('/documents', documentForm)
          ElMessage.success('添加成功')
        }
        
        showAddDialog.value = false
        resetForm()
        loadDocuments()
      } catch (error) {
        ElMessage.error(isEdit.value ? '更新失败' : '添加失败')
      } finally {
        submitLoading.value = false
      }
    }
  })
}

const resetForm = () => {
  Object.assign(documentForm, {
    id: null,
    title: '',
    type: '',
    category: '',
    content: '',
    summary: '',
    keywords: '',
    tags: '',
    author: '',
    version: '1.0',
    language: 'zh-CN',
    filePath: '',
    fileType: '',
    fileSize: null
  })
  isEdit.value = false
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

const getKeywords = (keywords) => {
  return keywords ? keywords.split(',').map(k => k.trim()).filter(k => k) : []
}

const getTags = (tags) => {
  return tags ? tags.split(',').map(t => t.trim()).filter(t => t) : []
}

onMounted(() => {
  loadDocuments()
})
</script>

<style scoped>
.documents {
  max-width: 1400px;
}

.toolbar {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.document-detail {
  max-height: 600px;
  overflow-y: auto;
}

.document-content {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  white-space: pre-wrap;
  max-height: 300px;
  overflow-y: auto;
}
</style>