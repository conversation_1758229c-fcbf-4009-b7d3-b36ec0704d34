package com.aquaculture.management.utils;

import java.util.regex.Pattern;

/**
 * 密码工具类
 * 确保密码验证规则在整个系统中保持一致
 */
public class PasswordUtil {

    /**
     * 密码复杂度正则表达式
     * 必须包含：至少一个大写字母、一个小写字母、一个数字
     * 长度：6-20个字符
     * 允许的特殊字符：@$!%*?&
     */
    public static final String PASSWORD_PATTERN = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{6,20}$";
    
    /**
     * 用户名正则表达式
     * 只能包含字母、数字和下划线
     * 长度：3-20个字符
     */
    public static final String USERNAME_PATTERN = "^[a-zA-Z0-9_]{3,20}$";
    
    /**
     * 手机号正则表达式
     * 中国大陆手机号格式
     */
    public static final String PHONE_PATTERN = "^1[3-9]\\d{9}$";

    private static final Pattern passwordPattern = Pattern.compile(PASSWORD_PATTERN);
    private static final Pattern usernamePattern = Pattern.compile(USERNAME_PATTERN);
    private static final Pattern phonePattern = Pattern.compile(PHONE_PATTERN);

    /**
     * 验证密码强度
     */
    public static boolean isValidPassword(String password) {
        if (password == null || password.trim().isEmpty()) {
            return false;
        }
        return passwordPattern.matcher(password).matches();
    }

    /**
     * 验证用户名格式
     */
    public static boolean isValidUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            return false;
        }
        return usernamePattern.matcher(username).matches();
    }

    /**
     * 验证手机号格式
     */
    public static boolean isValidPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return true; // 手机号是可选的
        }
        return phonePattern.matcher(phone).matches();
    }

    /**
     * 获取密码强度描述
     */
    public static String getPasswordStrengthDescription() {
        return "密码必须满足以下要求：\n" +
               "1. 长度6-20个字符\n" +
               "2. 包含至少一个大写字母(A-Z)\n" +
               "3. 包含至少一个小写字母(a-z)\n" +
               "4. 包含至少一个数字(0-9)\n" +
               "5. 可以包含特殊字符(@$!%*?&)";
    }

    /**
     * 获取用户名格式描述
     */
    public static String getUsernameFormatDescription() {
        return "用户名必须满足以下要求：\n" +
               "1. 长度3-20个字符\n" +
               "2. 只能包含字母、数字和下划线\n" +
               "3. 不能以数字开头";
    }

    /**
     * 检查密码复杂度并返回详细信息
     */
    public static PasswordValidationResult validatePassword(String password) {
        PasswordValidationResult result = new PasswordValidationResult();
        
        if (password == null || password.trim().isEmpty()) {
            result.setValid(false);
            result.addError("密码不能为空");
            return result;
        }

        // 检查长度
        if (password.length() < 6) {
            result.addError("密码长度不能少于6个字符");
        }
        if (password.length() > 20) {
            result.addError("密码长度不能超过20个字符");
        }

        // 检查是否包含小写字母
        if (!password.matches(".*[a-z].*")) {
            result.addError("密码必须包含至少一个小写字母");
        }

        // 检查是否包含大写字母
        if (!password.matches(".*[A-Z].*")) {
            result.addError("密码必须包含至少一个大写字母");
        }

        // 检查是否包含数字
        if (!password.matches(".*\\d.*")) {
            result.addError("密码必须包含至少一个数字");
        }

        // 检查是否包含非法字符
        if (!password.matches("^[a-zA-Z\\d@$!%*?&]*$")) {
            result.addError("密码只能包含字母、数字和特殊字符(@$!%*?&)");
        }

        result.setValid(result.getErrors().isEmpty());
        return result;
    }

    /**
     * 密码验证结果类
     */
    public static class PasswordValidationResult {
        private boolean valid;
        private java.util.List<String> errors = new java.util.ArrayList<>();

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public java.util.List<String> getErrors() {
            return errors;
        }

        public void addError(String error) {
            this.errors.add(error);
        }

        public String getErrorMessage() {
            return String.join("; ", errors);
        }
    }
}