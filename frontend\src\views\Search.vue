<template>
  <div class="search-page">
    <div class="search-header">
      <div class="search-form">
        <el-input
          v-model="searchForm.keyword"
          placeholder="请输入搜索关键词..."
          size="large"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prepend>
            <el-select 
              v-model="searchForm.type" 
              placeholder="类型" 
              style="width: 120px"
              clearable
            >
              <el-option label="全部" value="" />
              <el-option label="文化资源" value="cultural_resource" />
              <el-option label="文档资料" value="document" />
              <el-option label="活动" value="activity" />
            </el-select>
          </template>
          <template #append>
            <el-button type="primary" @click="handleSearch" :loading="searching">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
          </template>
        </el-input>
      </div>

      <div class="search-filters" v-if="searchForm.keyword">
        <el-space wrap>
          <el-tag
            v-for="tag in hotTags"
            :key="tag"
            :type="searchForm.keyword === tag ? 'primary' : 'info'"
            @click="selectTag(tag)"
            style="cursor: pointer;"
          >
            {{ tag }}
          </el-tag>
        </el-space>
      </div>

      <div class="search-stats" v-if="searchResults.length > 0">
        <span>找到约 {{ pagination.total }} 条结果，用时 {{ searchTime }}ms</span>
      </div>
    </div>

    <div class="search-content">
      <div v-if="!hasSearched" class="search-placeholder">
        <el-empty description="输入关键词开始搜索">
          <div class="hot-keywords">
            <h4>热门搜索</h4>
            <el-space wrap>
              <el-tag
                v-for="keyword in hotKeywords"
                :key="keyword"
                @click="selectTag(keyword)"
                style="cursor: pointer;"
                effect="plain"
              >
                {{ keyword }}
              </el-tag>
            </el-space>
          </div>
        </el-empty>
      </div>

      <div v-else-if="searchResults.length === 0" class="no-results">
        <el-empty description="未找到相关结果">
          <el-button type="primary" @click="clearSearch">
            清空搜索
          </el-button>
        </el-empty>
      </div>

      <div v-else class="search-results">
        <div class="result-filters">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-select v-model="searchForm.sortBy" placeholder="排序方式" @change="handleSearch">
                <el-option label="相关度" value="relevance" />
                <el-option label="时间降序" value="time_desc" />
                <el-option label="时间升序" value="time_asc" />
                <el-option label="浏览量" value="view_count" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select v-model="searchForm.region" placeholder="地区" clearable @change="handleSearch">
                <el-option label="全部地区" value="" />
                <el-option label="贵州" value="贵州" />
                <el-option label="广西" value="广西" />
                <el-option label="云南" value="云南" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select v-model="searchForm.period" placeholder="时期" clearable @change="handleSearch">
                <el-option label="全部时期" value="" />
                <el-option label="古代" value="古代" />
                <el-option label="近代" value="近代" />
                <el-option label="现代" value="现代" />
              </el-select>
            </el-col>
          </el-row>
        </div>

        <div class="result-list">
          <div 
            v-for="item in searchResults" 
            :key="`${item.type}_${item.id}`"
            class="result-item"
            @click="viewDetail(item)"
          >
            <div class="result-header">
              <div class="result-title">
                <el-tag :type="getTypeColor(item.type)" size="small">
                  {{ getTypeName(item.type) }}
                </el-tag>
                <h3 v-html="highlightKeyword(item.title || item.name)"></h3>
              </div>
              <div class="result-meta">
                <span class="result-time">{{ formatTime(item.createTime) }}</span>
                <span class="result-author">{{ item.author || item.createBy }}</span>
              </div>
            </div>
            
            <div class="result-content">
              <p v-html="highlightKeyword(item.description || item.summary || item.content)"></p>
            </div>
            
            <div class="result-footer">
              <div class="result-tags">
                <el-tag
                  v-for="tag in getTags(item.tags)"
                  :key="tag"
                  size="small"
                  effect="plain"
                  @click.stop="selectTag(tag)"
                  style="margin-right: 5px; cursor: pointer;"
                >
                  {{ tag }}
                </el-tag>
              </div>
              <div class="result-stats">
                <span v-if="item.viewCount">
                  <el-icon><View /></el-icon>
                  {{ item.viewCount }}
                </span>
                <span v-if="item.downloadCount">
                  <el-icon><Download /></el-icon>
                  {{ item.downloadCount }}
                </span>
                <span v-if="item.region">
                  <el-icon><Location /></el-icon>
                  {{ item.region }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div class="pagination">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSearch"
            @current-change="handleSearch"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'
import { format } from 'date-fns'

const route = useRoute()
const router = useRouter()

const searching = ref(false)
const hasSearched = ref(false)
const searchTime = ref(0)

const searchForm = reactive({
  keyword: '',
  type: '',
  sortBy: 'relevance',
  region: '',
  period: ''
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const searchResults = ref([])

const hotKeywords = ref(['水族文化', '传统节日', '民族服饰', '历史传说', '民族音乐', '传统工艺'])
const hotTags = ref(['节日', '服饰', '音乐', '工艺', '建筑', '文学'])

const handleSearch = async () => {
  if (!searchForm.keyword.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }

  searching.value = true
  hasSearched.value = true
  const startTime = Date.now()

  try {
    const params = {
      keyword: searchForm.keyword,
      type: searchForm.type,
      sortBy: searchForm.sortBy,
      region: searchForm.region,
      period: searchForm.period,
      current: pagination.current,
      size: pagination.size
    }

    // 清空值为空字符串的参数
    Object.keys(params).forEach(key => {
      if (params[key] === '') {
        delete params[key]
      }
    })

    const response = await request.get('/search', { params })
    searchResults.value = response.data.records || response.data
    pagination.total = response.data.total || 0
    
    searchTime.value = Date.now() - startTime

    // 更新URL参数
    router.push({
      path: '/search',
      query: { ...params, current: undefined, size: undefined }
    })
  } catch (error) {
    ElMessage.error('搜索失败')
    console.error('搜索错误:', error)
  } finally {
    searching.value = false
  }
}

const selectTag = (tag) => {
  searchForm.keyword = tag
  pagination.current = 1
  handleSearch()
}

const clearSearch = () => {
  searchForm.keyword = ''
  searchForm.type = ''
  searchForm.region = ''
  searchForm.period = ''
  searchResults.value = []
  hasSearched.value = false
  router.push('/search')
}

const viewDetail = (item) => {
  let route = ''
  switch (item.type) {
    case 'cultural_resource':
      route = `/cultural-resources/${item.id}`
      break
    case 'document':
      route = `/documents/${item.id}`
      break
    case 'activity':
      route = `/activities/${item.id}`
      break
    default:
      ElMessage.info('暂不支持查看该类型内容')
      return
  }
  
  window.open(route, '_blank')
}

const highlightKeyword = (text) => {
  if (!text || !searchForm.keyword) return text
  
  const keyword = searchForm.keyword.trim()
  if (!keyword) return text
  
  // 截取前200个字符用于显示
  let displayText = text.length > 200 ? text.substring(0, 200) + '...' : text
  
  // 高亮关键词
  const regex = new RegExp(`(${keyword})`, 'gi')
  return displayText.replace(regex, '<mark>$1</mark>')
}

const getTags = (tags) => {
  if (!tags) return []
  return tags.split(',').map(t => t.trim()).filter(t => t).slice(0, 3)
}

const getTypeName = (type) => {
  const typeMap = {
    'cultural_resource': '文化资源',
    'document': '文档资料',
    'activity': '活动'
  }
  return typeMap[type] || type
}

const getTypeColor = (type) => {
  const colorMap = {
    'cultural_resource': 'primary',
    'document': 'success',
    'activity': 'warning'
  }
  return colorMap[type] || 'info'
}

const formatTime = (time) => {
  if (!time) return ''
  try {
    return format(new Date(time), 'yyyy-MM-dd')
  } catch (error) {
    return time
  }
}

onMounted(() => {
  // 从URL参数中获取搜索条件
  const query = route.query
  if (query.keyword) {
    Object.assign(searchForm, {
      keyword: query.keyword || '',
      type: query.type || '',
      sortBy: query.sortBy || 'relevance',
      region: query.region || '',
      period: query.period || ''
    })
    handleSearch()
  }
})
</script>

<style scoped>
.search-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.search-header {
  margin-bottom: 30px;
}

.search-form {
  margin-bottom: 20px;
}

.search-form :deep(.el-input-group__prepend) {
  padding: 0;
}

.search-filters {
  margin-bottom: 15px;
}

.search-stats {
  color: #666;
  font-size: 14px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.search-placeholder {
  text-align: center;
  padding: 60px 0;
}

.hot-keywords {
  margin-top: 30px;
}

.hot-keywords h4 {
  margin-bottom: 15px;
  color: #333;
}

.no-results {
  text-align: center;
  padding: 60px 0;
}

.search-results {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.result-filters {
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.result-list {
  padding: 0;
}

.result-item {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.result-item:hover {
  background: #fafafa;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.result-item:last-child {
  border-bottom: none;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.result-title {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
}

.result-title h3 {
  margin: 0;
  color: #1890ff;
  font-size: 18px;
  font-weight: 500;
  line-height: 1.4;
}

.result-title h3:hover {
  text-decoration: underline;
}

.result-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
  color: #999;
  font-size: 12px;
}

.result-content {
  margin-bottom: 15px;
}

.result-content p {
  margin: 0;
  color: #555;
  line-height: 1.6;
  font-size: 14px;
}

.result-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-tags {
  flex: 1;
}

.result-stats {
  display: flex;
  gap: 15px;
  color: #999;
  font-size: 12px;
}

.result-stats span {
  display: flex;
  align-items: center;
  gap: 3px;
}

.pagination {
  padding: 20px;
  display: flex;
  justify-content: center;
}

:deep(mark) {
  background: #fff2d8;
  padding: 1px 2px;
  border-radius: 2px;
  color: #d46b08;
  font-weight: 500;
}

@media (max-width: 768px) {
  .search-page {
    padding: 10px;
  }
  
  .result-header {
    flex-direction: column;
    gap: 10px;
  }
  
  .result-meta {
    align-self: flex-start;
    flex-direction: row;
    gap: 10px;
  }
  
  .result-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>