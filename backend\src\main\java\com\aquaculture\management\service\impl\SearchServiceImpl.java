package com.aquaculture.management.service.impl;

import com.aquaculture.management.dto.Result;
import com.aquaculture.management.service.SearchService;
import com.aquaculture.management.service.CulturalResourceService;
import com.aquaculture.management.service.DocumentService;
import com.aquaculture.management.service.ActivityService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SearchServiceImpl implements SearchService {

    private final CulturalResourceService culturalResourceService;
    private final DocumentService documentService;
    private final ActivityService activityService;

    @Override
    public Result<IPage<Map<String, Object>>> search(String keyword, String type, String region, 
                                                   String period, String sortBy, Integer current, Integer size) {
        try {
            Page<Map<String, Object>> page = new Page<>(current, size);
            List<Map<String, Object>> allResults = new ArrayList<>();

            // 搜索文化资源
            if (type == null || "cultural_resource".equals(type)) {
                List<Map<String, Object>> culturalResults = searchCulturalResources(keyword, region, period);
                allResults.addAll(culturalResults);
            }

            // 搜索文档资料
            if (type == null || "document".equals(type)) {
                List<Map<String, Object>> documentResults = searchDocuments(keyword);
                allResults.addAll(documentResults);
            }

            // 搜索活动
            if (type == null || "activity".equals(type)) {
                List<Map<String, Object>> activityResults = searchActivities(keyword);
                allResults.addAll(activityResults);
            }

            // 排序
            sortResults(allResults, sortBy, keyword);

            // 分页
            int start = (current - 1) * size;
            int end = Math.min(start + size, allResults.size());
            List<Map<String, Object>> pageResults = allResults.subList(start, end);

            page.setRecords(pageResults);
            page.setTotal(allResults.size());

            // 记录搜索日志
            recordSearchLog(keyword, type, allResults.size());

            return Result.success(page);
        } catch (Exception e) {
            log.error("搜索失败", e);
            return Result.error("搜索失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<String>> getSearchSuggestions(String keyword) {
        try {
            Set<String> suggestions = new HashSet<>();
            
            // 从文化资源中获取建议
            suggestions.addAll(getSuggestions(keyword, "cultural_resource"));
            
            // 从文档中获取建议
            suggestions.addAll(getSuggestions(keyword, "document"));
            
            // 从活动中获取建议
            suggestions.addAll(getSuggestions(keyword, "activity"));
            
            List<String> result = suggestions.stream()
                    .filter(s -> s.contains(keyword))
                    .limit(10)
                    .collect(Collectors.toList());
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取搜索建议失败", e);
            return Result.error("获取搜索建议失败");
        }
    }

    @Override
    public Result<List<String>> getHotKeywords() {
        try {
            // 模拟热门关键词，实际项目中可以从搜索日志统计
            List<String> hotKeywords = Arrays.asList(
                "水族文化", "传统节日", "民族服饰", "历史传说", 
                "民族音乐", "传统工艺", "节日习俗", "民族建筑"
            );
            return Result.success(hotKeywords);
        } catch (Exception e) {
            log.error("获取热门关键词失败", e);
            return Result.error("获取热门关键词失败");
        }
    }

    @Override
    public Result<Map<String, Object>> getSearchStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 模拟搜索统计数据
            statistics.put("totalSearches", 10000);
            statistics.put("todaySearches", 150);
            statistics.put("popularKeywords", getHotKeywords().getData());
            
            Map<String, Integer> typeStats = new HashMap<>();
            typeStats.put("cultural_resource", 5000);
            typeStats.put("document", 3000);
            typeStats.put("activity", 2000);
            statistics.put("typeStatistics", typeStats);
            
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取搜索统计失败", e);
            return Result.error("获取搜索统计失败");
        }
    }

    @Override
    public void recordSearchLog(String keyword, String type, Integer resultCount) {
        try {
            // 这里可以记录搜索日志到数据库
            log.info("搜索记录: keyword={}, type={}, resultCount={}", keyword, type, resultCount);
        } catch (Exception e) {
            log.error("记录搜索日志失败", e);
        }
    }

    private List<Map<String, Object>> searchCulturalResources(String keyword, String region, String period) {
        List<Map<String, Object>> results = new ArrayList<>();
        
        try {
            // 构建查询条件
            LambdaQueryWrapper<Object> wrapper = new LambdaQueryWrapper<>();
            
            // 模拟搜索逻辑，实际项目中需要根据具体的实体类调整
            // 这里需要调用具体的服务方法进行搜索
            
            // 示例数据
            Map<String, Object> item = new HashMap<>();
            item.put("id", 1L);
            item.put("type", "cultural_resource");
            item.put("title", "水族文化传统节日");
            item.put("description", "介绍水族传统节日的起源和发展");
            item.put("createTime", new Date());
            item.put("author", "文化学者");
            item.put("viewCount", 1000);
            item.put("region", "贵州");
            item.put("tags", "节日,传统,文化");
            results.add(item);
            
        } catch (Exception e) {
            log.error("搜索文化资源失败", e);
        }
        
        return results;
    }

    private List<Map<String, Object>> searchDocuments(String keyword) {
        List<Map<String, Object>> results = new ArrayList<>();
        
        try {
            // 模拟文档搜索
            Map<String, Object> item = new HashMap<>();
            item.put("id", 1L);
            item.put("type", "document");
            item.put("title", "水族历史文献");
            item.put("content", "详细记录了水族的历史发展过程");
            item.put("createTime", new Date());
            item.put("author", "历史学家");
            item.put("downloadCount", 500);
            item.put("tags", "历史,文献,研究");
            results.add(item);
            
        } catch (Exception e) {
            log.error("搜索文档失败", e);
        }
        
        return results;
    }

    private List<Map<String, Object>> searchActivities(String keyword) {
        List<Map<String, Object>> results = new ArrayList<>();
        
        try {
            // 模拟活动搜索
            Map<String, Object> item = new HashMap<>();
            item.put("id", 1L);
            item.put("type", "activity");
            item.put("name", "水族文化节");
            item.put("description", "一年一度的水族传统文化节庆活动");
            item.put("createTime", new Date());
            item.put("organizer", "文化中心");
            item.put("location", "贵州省");
            item.put("tags", "文化节,庆典,传统");
            results.add(item);
            
        } catch (Exception e) {
            log.error("搜索活动失败", e);
        }
        
        return results;
    }

    private void sortResults(List<Map<String, Object>> results, String sortBy, String keyword) {
        if ("relevance".equals(sortBy)) {
            // 按相关度排序（简单实现：包含关键词越多相关度越高）
            results.sort((a, b) -> {
                int scoreA = calculateRelevanceScore(a, keyword);
                int scoreB = calculateRelevanceScore(b, keyword);
                return Integer.compare(scoreB, scoreA);
            });
        } else if ("time_desc".equals(sortBy)) {
            results.sort((a, b) -> {
                Date dateA = (Date) a.get("createTime");
                Date dateB = (Date) b.get("createTime");
                return dateB.compareTo(dateA);
            });
        } else if ("time_asc".equals(sortBy)) {
            results.sort((a, b) -> {
                Date dateA = (Date) a.get("createTime");
                Date dateB = (Date) b.get("createTime");
                return dateA.compareTo(dateB);
            });
        } else if ("view_count".equals(sortBy)) {
            results.sort((a, b) -> {
                Integer countA = (Integer) a.getOrDefault("viewCount", 0);
                Integer countB = (Integer) b.getOrDefault("viewCount", 0);
                return Integer.compare(countB, countA);
            });
        }
    }

    private int calculateRelevanceScore(Map<String, Object> item, String keyword) {
        int score = 0;
        String title = (String) item.get("title");
        String description = (String) item.get("description");
        String content = (String) item.get("content");
        
        if (title != null && title.toLowerCase().contains(keyword.toLowerCase())) {
            score += 10;
        }
        if (description != null && description.toLowerCase().contains(keyword.toLowerCase())) {
            score += 5;
        }
        if (content != null && content.toLowerCase().contains(keyword.toLowerCase())) {
            score += 3;
        }
        
        return score;
    }

    private List<String> getSuggestions(String keyword, String type) {
        // 模拟获取搜索建议
        List<String> suggestions = new ArrayList<>();
        
        if (keyword.contains("水族")) {
            suggestions.addAll(Arrays.asList("水族文化", "水族历史", "水族传统", "水族节日"));
        }
        if (keyword.contains("文化")) {
            suggestions.addAll(Arrays.asList("文化传承", "文化节", "文化研究", "文化保护"));
        }
        if (keyword.contains("传统")) {
            suggestions.addAll(Arrays.asList("传统工艺", "传统节日", "传统音乐", "传统服饰"));
        }
        
        return suggestions.stream().distinct().collect(Collectors.toList());
    }
}