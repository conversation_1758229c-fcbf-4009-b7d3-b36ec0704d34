package com.aquaculture.management.service.impl;

import com.aquaculture.management.dto.Result;
import com.aquaculture.management.entity.ContentAudit;
import com.aquaculture.management.entity.CulturalResource;
import com.aquaculture.management.entity.Document;
import com.aquaculture.management.entity.Activity;
import com.aquaculture.management.mapper.ContentAuditMapper;
import com.aquaculture.management.service.ContentAuditService;
import com.aquaculture.management.service.CulturalResourceService;
import com.aquaculture.management.service.DocumentService;
import com.aquaculture.management.service.ActivityService;
import com.aquaculture.management.utils.UserContext;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ContentAuditServiceImpl extends ServiceImpl<ContentAuditMapper, ContentAudit> 
        implements ContentAuditService {

    private final CulturalResourceService culturalResourceService;
    private final DocumentService documentService;
    private final ActivityService activityService;

    @Override
    @Transactional
    public Result<Void> submitForAudit(String contentType, Long contentId, String contentTitle, 
                                     String contentSummary, Integer priority) {
        try {
            // 检查是否已存在待审核记录
            LambdaQueryWrapper<ContentAudit> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ContentAudit::getContentType, contentType)
                   .eq(ContentAudit::getContentId, contentId)
                   .eq(ContentAudit::getAuditStatus, 0)
                   .eq(ContentAudit::getDeleted, 0);
            
            if (count(wrapper) > 0) {
                return Result.error("该内容已有待审核记录");
            }

            ContentAudit audit = new ContentAudit();
            audit.setContentType(contentType);
            audit.setContentId(contentId);
            audit.setContentTitle(contentTitle);
            audit.setContentSummary(contentSummary);
            audit.setPriority(priority != null ? priority : 2);
            audit.setAuditStatus(0); // 待审核
            audit.setSubmitTime(LocalDateTime.now());
            audit.setSubmitUserId(UserContext.getCurrentUserId());
            audit.setSubmitUserName(UserContext.getCurrentUserName());
            audit.setManualRequired(true);

            // 自动审核评分
            Double autoScore = calculateAutoAuditScore(contentSummary, contentTitle);
            audit.setAutoAuditScore(autoScore);
            
            // 风险等级评估
            audit.setRiskLevel(calculateRiskLevel(autoScore));
            
            // 如果自动评分很高，可以考虑自动通过
            if (autoScore >= 90.0) {
                audit.setAuditStatus(1); // 自动通过
                audit.setAuditTime(LocalDateTime.now());
                audit.setAuditReason("自动审核通过");
                audit.setManualRequired(false);
            }

            save(audit);

            // 更新内容状态为待审核
            updateContentStatus(contentType, contentId, 2);

            return Result.success();
        } catch (Exception e) {
            log.error("提交审核失败", e);
            return Result.error("提交审核失败: " + e.getMessage());
        }
    }

    @Override
    public Result<IPage<ContentAudit>> getAuditPage(Integer current, Integer size, 
                                                  String contentType, Integer auditStatus, 
                                                  Integer priority, Long submitUserId) {
        try {
            Page<ContentAudit> page = new Page<>(current, size);
            
            LambdaQueryWrapper<ContentAudit> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ContentAudit::getDeleted, 0)
                   .eq(contentType != null, ContentAudit::getContentType, contentType)
                   .eq(auditStatus != null, ContentAudit::getAuditStatus, auditStatus)
                   .eq(priority != null, ContentAudit::getPriority, priority)
                   .eq(submitUserId != null, ContentAudit::getSubmitUserId, submitUserId)
                   .orderByDesc(ContentAudit::getSubmitTime);

            IPage<ContentAudit> result = page(page, wrapper);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询审核记录失败", e);
            return Result.error("查询审核记录失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> approveAudit(Long auditId, String auditReason) {
        return processAudit(auditId, 1, auditReason);
    }

    @Override
    @Transactional
    public Result<Void> rejectAudit(Long auditId, String auditReason) {
        return processAudit(auditId, 2, auditReason);
    }

    @Override
    @Transactional
    public Result<Void> batchAudit(List<Long> auditIds, Integer auditStatus, String auditReason) {
        try {
            for (Long auditId : auditIds) {
                processAudit(auditId, auditStatus, auditReason);
            }
            return Result.success();
        } catch (Exception e) {
            log.error("批量审核失败", e);
            return Result.error("批量审核失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Map<String, Object>> getAuditStatistics() {
        try {
            List<Map<String, Object>> statusStats = baseMapper.getAuditStatistics();
            List<Map<String, Object>> typeStats = baseMapper.getAuditStatisticsByType(0);

            Map<String, Object> result = new HashMap<>();
            result.put("statusStatistics", statusStats);
            result.put("typeStatistics", typeStats);
            
            // 计算总体统计
            Long totalCount = count(new LambdaQueryWrapper<ContentAudit>().eq(ContentAudit::getDeleted, 0));
            Long pendingCount = count(new LambdaQueryWrapper<ContentAudit>()
                    .eq(ContentAudit::getDeleted, 0)
                    .eq(ContentAudit::getAuditStatus, 0));
            Long approvedCount = count(new LambdaQueryWrapper<ContentAudit>()
                    .eq(ContentAudit::getDeleted, 0)
                    .eq(ContentAudit::getAuditStatus, 1));
            Long rejectedCount = count(new LambdaQueryWrapper<ContentAudit>()
                    .eq(ContentAudit::getDeleted, 0)
                    .eq(ContentAudit::getAuditStatus, 2));

            Map<String, Long> summary = new HashMap<>();
            summary.put("total", totalCount);
            summary.put("pending", pendingCount);
            summary.put("approved", approvedCount);
            summary.put("rejected", rejectedCount);
            
            result.put("summary", summary);

            return Result.success(result);
        } catch (Exception e) {
            log.error("获取审核统计失败", e);
            return Result.error("获取审核统计失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> autoAudit(Long auditId) {
        try {
            ContentAudit audit = getById(auditId);
            if (audit == null) {
                return Result.error("审核记录不存在");
            }

            if (audit.getAuditStatus() != 0) {
                return Result.error("该记录已审核");
            }

            // 重新计算自动审核评分
            Double autoScore = calculateAutoAuditScore(audit.getContentSummary(), audit.getContentTitle());
            audit.setAutoAuditScore(autoScore);

            if (autoScore >= 80.0) {
                audit.setAuditStatus(1);
                audit.setAuditReason("自动审核通过");
                updateContentStatus(audit.getContentType(), audit.getContentId(), 1);
            } else if (autoScore <= 40.0) {
                audit.setAuditStatus(2);
                audit.setAuditReason("自动审核拒绝");
                updateContentStatus(audit.getContentType(), audit.getContentId(), 0);
            } else {
                audit.setManualRequired(true);
                return Result.error("需要人工审核");
            }

            audit.setAuditTime(LocalDateTime.now());
            audit.setAuditUserId(0L); // 系统自动审核
            audit.setAuditUserName("系统自动审核");

            updateById(audit);
            return Result.success();
        } catch (Exception e) {
            log.error("自动审核失败", e);
            return Result.error("自动审核失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Long> getPendingCount() {
        try {
            Long count = count(new LambdaQueryWrapper<ContentAudit>()
                    .eq(ContentAudit::getDeleted, 0)
                    .eq(ContentAudit::getAuditStatus, 0));
            return Result.success(count);
        } catch (Exception e) {
            log.error("获取待审核数量失败", e);
            return Result.error("获取待审核数量失败: " + e.getMessage());
        }
    }

    @Override
    public Result<IPage<ContentAudit>> getMyAuditRecords(Integer current, Integer size, Integer auditStatus) {
        try {
            Page<ContentAudit> page = new Page<>(current, size);
            
            LambdaQueryWrapper<ContentAudit> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ContentAudit::getDeleted, 0)
                   .eq(ContentAudit::getSubmitUserId, UserContext.getCurrentUserId())
                   .eq(auditStatus != null, ContentAudit::getAuditStatus, auditStatus)
                   .orderByDesc(ContentAudit::getSubmitTime);

            IPage<ContentAudit> result = page(page, wrapper);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询我的审核记录失败", e);
            return Result.error("查询我的审核记录失败: " + e.getMessage());
        }
    }

    private Result<Void> processAudit(Long auditId, Integer auditStatus, String auditReason) {
        try {
            ContentAudit audit = getById(auditId);
            if (audit == null) {
                return Result.error("审核记录不存在");
            }

            if (audit.getAuditStatus() != 0) {
                return Result.error("该记录已审核");
            }

            audit.setAuditStatus(auditStatus);
            audit.setAuditReason(auditReason);
            audit.setAuditTime(LocalDateTime.now());
            audit.setAuditUserId(UserContext.getCurrentUserId());
            audit.setAuditUserName(UserContext.getCurrentUserName());

            updateById(audit);

            // 更新内容状态
            Integer contentStatus = auditStatus == 1 ? 1 : 0; // 1-通过，0-拒绝
            updateContentStatus(audit.getContentType(), audit.getContentId(), contentStatus);

            return Result.success();
        } catch (Exception e) {
            log.error("审核处理失败", e);
            return Result.error("审核处理失败: " + e.getMessage());
        }
    }

    private void updateContentStatus(String contentType, Long contentId, Integer status) {
        try {
            switch (contentType) {
                case "cultural_resource":
                    CulturalResource resource = culturalResourceService.getById(contentId);
                    if (resource != null) {
                        resource.setStatus(status);
                        culturalResourceService.updateById(resource);
                    }
                    break;
                case "document":
                    Document document = documentService.getById(contentId);
                    if (document != null) {
                        document.setStatus(status);
                        documentService.updateById(document);
                    }
                    break;
                case "activity":
                    Activity activity = activityService.getById(contentId);
                    if (activity != null) {
                        activity.setStatus(status);
                        activityService.updateById(activity);
                    }
                    break;
            }
        } catch (Exception e) {
            log.error("更新内容状态失败: {}", e.getMessage());
        }
    }

    private Double calculateAutoAuditScore(String contentSummary, String contentTitle) {
        double score = 70.0; // 基础分数

        if (contentSummary != null && !contentSummary.trim().isEmpty()) {
            score += 10.0; // 有内容摘要加分
            
            // 检查敏感词（简单实现）
            String[] sensitiveWords = {"敏感", "违法", "暴力", "色情"};
            for (String word : sensitiveWords) {
                if (contentSummary.contains(word) || (contentTitle != null && contentTitle.contains(word))) {
                    score -= 30.0;
                    break;
                }
            }
        }

        if (contentTitle != null && contentTitle.length() > 5) {
            score += 5.0; // 标题适当加分
        }

        // 内容长度评分
        if (contentSummary != null) {
            int length = contentSummary.length();
            if (length >= 50 && length <= 1000) {
                score += 10.0;
            } else if (length > 1000) {
                score += 5.0;
            }
        }

        return Math.max(0.0, Math.min(100.0, score));
    }

    private Integer calculateRiskLevel(Double autoScore) {
        if (autoScore >= 80.0) {
            return 1; // 低风险
        } else if (autoScore >= 60.0) {
            return 2; // 中风险
        } else {
            return 3; // 高风险
        }
    }
}