package com.aquaculture.management.service.impl;

import com.aquaculture.management.entity.Document;
import com.aquaculture.management.mapper.DocumentMapper;
import com.aquaculture.management.service.DocumentService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

@Service
@RequiredArgsConstructor
public class DocumentServiceImpl extends ServiceImpl<DocumentMapper, Document> implements DocumentService {

    @Override
    public IPage<Document> getDocuments(int current, int size, String keyword, String type, String category) {
        Page<Document> page = new Page<>(current, size);
        LambdaQueryWrapper<Document> wrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(keyword)) {
            wrapper.like(Document::getTitle, keyword)
                   .or()
                   .like(Document::getContent, keyword)
                   .or()
                   .like(Document::getKeywords, keyword);
        }

        if (StringUtils.hasText(type)) {
            wrapper.eq(Document::getType, type);
        }

        if (StringUtils.hasText(category)) {
            wrapper.eq(Document::getCategory, category);
        }

        wrapper.eq(Document::getStatus, 1)
               .orderByDesc(Document::getCreateTime);

        return this.page(page, wrapper);
    }

    @Override
    public boolean createDocument(Document document) {
        document.setStatus(1);
        document.setViewCount(0);
        document.setDownloadCount(0);
        return this.save(document);
    }

    @Override
    public boolean updateDocument(Document document) {
        Document existingDocument = this.getById(document.getId());
        if (existingDocument == null) {
            throw new RuntimeException("文档不存在");
        }
        return this.updateById(document);
    }

    @Override
    public boolean deleteDocument(Long id) {
        return this.removeById(id);
    }

    @Override
    public void incrementViewCount(Long id) {
        Document document = this.getById(id);
        if (document != null) {
            document.setViewCount(document.getViewCount() + 1);
            this.updateById(document);
        }
    }

    @Override
    public void incrementDownloadCount(Long id) {
        Document document = this.getById(id);
        if (document != null) {
            document.setDownloadCount(document.getDownloadCount() + 1);
            this.updateById(document);
        }
    }

    @Override
    public List<Document> getRecentDocuments(int limit) {
        LambdaQueryWrapper<Document> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Document::getStatus, 1)
               .orderByDesc(Document::getCreateTime)
               .last("LIMIT " + limit);
        return this.list(wrapper);
    }
}