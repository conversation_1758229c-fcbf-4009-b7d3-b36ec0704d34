package com.aquaculture.management.mapper;

import com.aquaculture.management.entity.Statistics;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.Map;

@Mapper
public interface StatisticsMapper extends BaseMapper<Statistics> {

    @Select("SELECT COUNT(*) as total FROM sys_user WHERE deleted = 0")
    Long getTotalUsers();

    @Select("SELECT COUNT(*) as total FROM cultural_resource WHERE deleted = 0")
    Long getTotalResources();

    @Select("SELECT COUNT(*) as total FROM document WHERE deleted = 0")
    Long getTotalDocuments();

    @Select("SELECT COUNT(*) as total FROM activity WHERE deleted = 0")
    Long getTotalActivities();

    @Select("SELECT COALESCE(SUM(view_count), 0) as total FROM cultural_resource WHERE deleted = 0")
    Long getTotalResourceViews();

    @Select("SELECT COALESCE(SUM(download_count), 0) as total FROM cultural_resource WHERE deleted = 0")
    Long getTotalResourceDownloads();

    @Select("SELECT COALESCE(SUM(view_count), 0) as total FROM document WHERE deleted = 0")
    Long getTotalDocumentViews();

    @Select("SELECT COALESCE(SUM(download_count), 0) as total FROM document WHERE deleted = 0")
    Long getTotalDocumentDownloads();
}