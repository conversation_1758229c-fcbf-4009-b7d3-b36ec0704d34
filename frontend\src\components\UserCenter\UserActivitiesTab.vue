<template>
  <div class="user-activities">
    <div class="activities-header">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-select v-model="selectedType" placeholder="活动类型" clearable @change="loadActivities">
            <el-option label="全部" value="" />
            <el-option label="发布资源" value="publish_resource" />
            <el-option label="收藏" value="favorite" />
            <el-option label="关注" value="follow" />
            <el-option label="评论" value="comment" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <span class="total-count">共 {{ pagination.total }} 条动态</span>
        </el-col>
      </el-row>
    </div>

    <div class="activities-content">
      <div class="activity-timeline" v-loading="loading">
        <el-timeline>
          <el-timeline-item
            v-for="activity in activitiesList"
            :key="activity.id"
            :timestamp="formatTime(activity.createTime)"
            placement="top"
          >
            <el-card class="activity-card" shadow="hover">
              <div class="activity-content">
                <div class="activity-header">
                  <el-icon :class="getActivityIcon(activity.type)"></el-icon>
                  <span class="activity-type">{{ getActivityTypeName(activity.type) }}</span>
                  <el-tag :type="getActivityTagType(activity.type)" size="small">
                    {{ getActivityStatus(activity.status) }}
                  </el-tag>
                </div>
                
                <div class="activity-description">
                  <p>{{ activity.description }}</p>
                  
                  <div v-if="activity.targetTitle" class="activity-target">
                    <el-link 
                      :href="getTargetLink(activity)" 
                      :underline="false"
                      type="primary"
                    >
                      {{ activity.targetTitle }}
                    </el-link>
                  </div>
                  
                  <div v-if="activity.tags" class="activity-tags">
                    <el-tag
                      v-for="tag in getTags(activity.tags)"
                      :key="tag"
                      size="small"
                      effect="plain"
                      style="margin-right: 5px;"
                    >
                      {{ tag }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
        
        <div v-if="activitiesList.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无动态记录">
            <el-button type="primary" @click="$router.push('/cultural-resources')">
              去发现好内容
            </el-button>
          </el-empty>
        </div>
      </div>

      <div class="pagination" v-if="pagination.total > 0">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadActivities"
          @current-change="loadActivities"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

const props = defineProps({
  userId: {
    type: Number,
    default: null
  }
})

const router = useRouter()
const loading = ref(false)
const selectedType = ref('')

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const activitiesList = ref([])

const loadActivities = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      type: selectedType.value
    }
    
    if (props.userId) {
      params.userId = props.userId
    }
    
    const response = await request.get('/user-center/activities', { params })
    activitiesList.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载活动记录失败')
  } finally {
    loading.value = false
  }
}

const getActivityTypeName = (type) => {
  const typeMap = {
    'publish_resource': '发布了资源',
    'favorite': '收藏了',
    'follow': '关注了',
    'comment': '评论了'
  }
  return typeMap[type] || type
}

const getActivityIcon = (type) => {
  const iconMap = {
    'publish_resource': 'el-icon-upload2',
    'favorite': 'el-icon-star-on',
    'follow': 'el-icon-user',
    'comment': 'el-icon-chat-dot-round'
  }
  return iconMap[type] || 'el-icon-info'
}

const getActivityTagType = (type) => {
  const tagTypeMap = {
    'publish_resource': 'success',
    'favorite': 'warning',
    'follow': 'primary',
    'comment': 'info'
  }
  return tagTypeMap[type] || 'info'
}

const getActivityStatus = (status) => {
  const statusMap = {
    'active': '正常',
    'deleted': '已删除',
    'private': '私有'
  }
  return statusMap[status] || status
}

const getTargetLink = (activity) => {
  switch (activity.targetType) {
    case 'cultural_resource':
      return `/cultural-resources/${activity.targetId}`
    case 'document':
      return `/documents/${activity.targetId}`
    case 'activity':
      return `/activities/${activity.targetId}`
    case 'user':
      return `/user-center/${activity.targetId}`
    default:
      return '#'
  }
}

const getTags = (tags) => {
  return tags ? tags.split(',').map(t => t.trim()).filter(t => t) : []
}

const formatTime = (time) => {
  if (!time) return ''
  try {
    return formatDistanceToNow(new Date(time), {
      addSuffix: true,
      locale: zhCN
    })
  } catch (error) {
    return time
  }
}

onMounted(() => {
  loadActivities()
})
</script>

<style scoped>
.user-activities {
  padding: 20px 0;
}

.activities-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.total-count {
  color: #666;
  font-size: 14px;
  line-height: 32px;
}

.activities-content {
  min-height: 400px;
}

.activity-timeline {
  padding: 20px 0;
}

.activity-card {
  margin-bottom: 10px;
  border-left: 3px solid var(--el-color-primary);
}

.activity-content {
  padding: 10px;
}

.activity-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.activity-type {
  font-weight: 500;
  color: #333;
}

.activity-description p {
  margin: 0 0 10px 0;
  color: #666;
  line-height: 1.5;
}

.activity-target {
  margin: 10px 0;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid var(--el-color-primary);
}

.activity-tags {
  margin-top: 10px;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.pagination {
  margin-top: 30px;
  text-align: center;
}

:deep(.el-timeline-item__timestamp) {
  color: #999;
  font-size: 12px;
}

:deep(.el-timeline-item__node) {
  background-color: var(--el-color-primary);
}

@media (max-width: 768px) {
  .activity-timeline {
    padding: 10px 0;
  }
  
  .activity-header {
    flex-wrap: wrap;
  }
}
</style>