<template>
  <div class="user-following">
    <div class="following-list" v-loading="loading">
      <div v-if="followingList.length === 0 && !loading" class="empty-state">
        <el-empty description="还没有关注任何人">
          <el-button type="primary" @click="$router.push('/users')">
            去发现用户
          </el-button>
        </el-empty>
      </div>

      <el-row :gutter="20" v-else>
        <el-col :span="8" v-for="follow in followingList" :key="follow.id">
          <el-card class="follow-card" shadow="hover">
            <div class="user-info">
              <el-avatar :size="60" :src="follow.avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <div class="user-details">
                <h4>{{ follow.username }}</h4>
                <p class="follow-time">{{ formatTime(follow.createTime) }}关注</p>
              </div>
            </div>
            <div class="follow-actions" v-if="isCurrentUser">
              <el-button size="small" @click="viewUser(follow.followeeId)">
                查看
              </el-button>
              <el-button size="small" type="danger" @click="unfollowUser(follow)">
                取消关注
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <div class="pagination" v-if="pagination.total > 0">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[12, 24, 48]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadFollowing"
          @current-change="loadFollowing"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

const props = defineProps({
  userId: {
    type: Number,
    default: null
  },
  isCurrentUser: {
    type: Boolean,
    default: true
  }
})

const router = useRouter()
const loading = ref(false)

const pagination = reactive({
  current: 1,
  size: 12,
  total: 0
})

const followingList = ref([])

const loadFollowing = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size
    }
    
    if (props.userId) {
      params.userId = props.userId
    }
    
    const response = await request.get('/user-center/following', { params })
    followingList.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载关注列表失败')
  } finally {
    loading.value = false
  }
}

const unfollowUser = async (follow) => {
  try {
    await ElMessageBox.confirm('确定要取消关注吗？', '确认操作', {
      type: 'warning'
    })
    
    await request.delete('/user-center/follow', {
      params: { followeeId: follow.followeeId }
    })
    
    ElMessage.success('取消关注成功')
    loadFollowing()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消关注失败')
    }
  }
}

const viewUser = (userId) => {
  router.push(`/user-center/${userId}`)
}

const formatTime = (time) => {
  if (!time) return ''
  try {
    return formatDistanceToNow(new Date(time), {
      addSuffix: true,
      locale: zhCN
    })
  } catch (error) {
    return time
  }
}

onMounted(() => {
  loadFollowing()
})
</script>

<style scoped>
.user-following {
  padding: 20px 0;
}

.follow-card {
  margin-bottom: 20px;
  text-align: center;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15px;
}

.user-details {
  margin-top: 10px;
}

.user-details h4 {
  margin: 0 0 5px 0;
  color: #333;
}

.follow-time {
  margin: 0;
  color: #999;
  font-size: 12px;
}

.follow-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.pagination {
  margin-top: 30px;
  text-align: center;
}

@media (max-width: 768px) {
  .following-list :deep(.el-col-8) {
    width: 100%;
  }
}
</style>