import { defineStore } from 'pinia'
import { login } from '@/api/auth'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: '',
    userInfo: null
  }),

  getters: {
    isAuthenticated: (state) => !!state.token
  },

  actions: {
    setToken(token) {
      this.token = token
      localStorage.setItem('token', token)
    },

    setUserInfo(userInfo) {
      this.userInfo = userInfo
    },

    async login(loginForm) {
      try {
        const response = await login(loginForm)
        if (response.code === 200) {
          this.setToken(response.data.token)
          this.setUserInfo(response.data)
          return Promise.resolve(response.data)
        } else {
          return Promise.reject(new Error(response.message))
        }
      } catch (error) {
        return Promise.reject(error)
      }
    },

    logout() {
      this.token = ''
      this.userInfo = null
      localStorage.removeItem('token')
    }
  }
})