<template>
  <div class="cultural-resources">
    <div class="page-header">
      <h2>文化资源管理</h2>
      <p>管理水族传统文化资源</p>
    </div>

    <div class="page-content">
      <!-- 搜索和操作栏 -->
      <div class="toolbar">
        <el-row :gutter="20" style="margin-bottom: 20px;">
          <el-col :span="6">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索资源名称、描述或关键词"
              prefix-icon="Search"
              @input="handleSearch"
            />
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.type" placeholder="资源类型" clearable @change="handleSearch">
              <el-option label="传统工艺" value="traditional_craft" />
              <el-option label="民歌" value="folk_song" />
              <el-option label="舞蹈" value="dance" />
              <el-option label="节庆" value="festival" />
              <el-option label="故事传说" value="story" />
              <el-option label="服饰" value="clothing" />
              <el-option label="建筑" value="architecture" />
              <el-option label="饮食文化" value="food" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.region" placeholder="地区" clearable @change="handleSearch">
              <el-option label="贵州省" value="贵州省" />
              <el-option label="云南省" value="云南省" />
              <el-option label="广西省" value="广西省" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" icon="Plus" @click="showAddDialog = true">
              添加资源
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 资源列表 -->
      <el-table
        :data="resourceList"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="name" label="资源名称" width="200" />
        <el-table-column prop="type" label="资源类型" width="120" />
        <el-table-column prop="category" label="分类" width="120" />
        <el-table-column prop="region" label="地区" width="100" />
        <el-table-column prop="viewCount" label="浏览次数" width="100" />
        <el-table-column prop="downloadCount" label="下载次数" width="100" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" type="primary" @click="viewResource(scope.row)">
              查看
            </el-button>
            <el-button size="small" type="warning" @click="editResource(scope.row)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="deleteResource(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadResources"
          @current-change="loadResources"
        />
      </div>
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="isEdit ? '编辑资源' : '添加资源'"
      width="600px"
    >
      <el-form
        ref="resourceFormRef"
        :model="resourceForm"
        :rules="resourceRules"
        label-width="100px"
      >
        <el-form-item label="资源名称" prop="name">
          <el-input v-model="resourceForm.name" />
        </el-form-item>
        <el-form-item label="资源类型" prop="type">
          <el-select v-model="resourceForm.type" style="width: 100%">
            <el-option label="传统工艺" value="traditional_craft" />
            <el-option label="民歌" value="folk_song" />
            <el-option label="舞蹈" value="dance" />
            <el-option label="节庆" value="festival" />
            <el-option label="故事传说" value="story" />
            <el-option label="服饰" value="clothing" />
            <el-option label="建筑" value="architecture" />
            <el-option label="饮食文化" value="food" />
          </el-select>
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-input v-model="resourceForm.category" />
        </el-form-item>
        <el-form-item label="地区" prop="region">
          <el-input v-model="resourceForm.region" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="resourceForm.description" type="textarea" :rows="4" />
        </el-form-item>
        <el-form-item label="关键词" prop="keywords">
          <el-input v-model="resourceForm.keywords" placeholder="用逗号分隔多个关键词" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveResource">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'

const loading = ref(false)
const showAddDialog = ref(false)
const isEdit = ref(false)
const resourceFormRef = ref()

const searchForm = reactive({
  keyword: '',
  type: '',
  region: ''
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const resourceList = ref([])

const resourceForm = reactive({
  id: null,
  name: '',
  type: '',
  category: '',
  region: '',
  description: '',
  keywords: ''
})

const resourceRules = {
  name: [{ required: true, message: '请输入资源名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择资源类型', trigger: 'change' }]
}

const loadResources = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    const response = await request.get('/cultural-resources', { params })
    resourceList.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载资源列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadResources()
}

const viewResource = (resource) => {
  // 实现查看详情逻辑
  ElMessage.info('查看功能开发中')
}

const editResource = (resource) => {
  isEdit.value = true
  Object.assign(resourceForm, resource)
  showAddDialog.value = true
}

const deleteResource = async (resource) => {
  try {
    await ElMessageBox.confirm(`确定要删除资源"${resource.name}"吗？`, '确认删除', {
      type: 'warning'
    })
    
    await request.delete(`/cultural-resources/${resource.id}`)
    ElMessage.success('删除成功')
    loadResources()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const saveResource = async () => {
  if (!resourceFormRef.value) return
  
  await resourceFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (isEdit.value) {
          await request.put(`/cultural-resources/${resourceForm.id}`, resourceForm)
          ElMessage.success('更新成功')
        } else {
          await request.post('/cultural-resources', resourceForm)
          ElMessage.success('添加成功')
        }
        
        showAddDialog.value = false
        resetForm()
        loadResources()
      } catch (error) {
        ElMessage.error(isEdit.value ? '更新失败' : '添加失败')
      }
    }
  })
}

const resetForm = () => {
  Object.assign(resourceForm, {
    id: null,
    name: '',
    type: '',
    category: '',
    region: '',
    description: '',
    keywords: ''
  })
  isEdit.value = false
}

onMounted(() => {
  loadResources()
})
</script>

<style scoped>
.cultural-resources {
  max-width: 1200px;
}

.toolbar {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>