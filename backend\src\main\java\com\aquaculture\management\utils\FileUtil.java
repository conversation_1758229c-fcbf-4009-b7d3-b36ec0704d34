package com.aquaculture.management.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

@Component
public class FileUtil {

    @Value("${file.upload.path}")
    private String uploadPath;

    @Value("${file.upload.url}")
    private String uploadUrl;

    private static final List<String> ALLOWED_EXTENSIONS = Arrays.asList(
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp",
            ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
            ".txt", ".md", ".zip", ".rar", ".7z",
            ".mp3", ".wav", ".mp4", ".avi", ".mov", ".wmv"
    );

    private static final long MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

    public String uploadFile(MultipartFile file, String category) throws IOException {
        if (file.isEmpty()) {
            throw new RuntimeException("文件不能为空");
        }

        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new RuntimeException("文件大小不能超过50MB");
        }

        // 获取原始文件名和扩展名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.isEmpty()) {
            throw new RuntimeException("文件名不能为空");
        }

        String extension = getFileExtension(originalFilename);
        if (!ALLOWED_EXTENSIONS.contains(extension.toLowerCase())) {
            throw new RuntimeException("不支持的文件类型: " + extension);
        }

        // 生成新文件名
        String newFileName = UUID.randomUUID().toString() + extension;

        // 按日期和类别创建目录结构
        String dateDir = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String relativePath = category + "/" + dateDir + "/" + newFileName;
        
        // 创建完整路径
        Path targetPath = Paths.get(uploadPath, relativePath);
        Files.createDirectories(targetPath.getParent());

        // 保存文件
        file.transferTo(targetPath.toFile());

        // 返回访问URL
        return uploadUrl + relativePath.replace("\\", "/");
    }

    public boolean deleteFile(String fileUrl) {
        if (fileUrl == null || !fileUrl.startsWith(uploadUrl)) {
            return false;
        }

        try {
            String relativePath = fileUrl.substring(uploadUrl.length());
            Path filePath = Paths.get(uploadPath, relativePath);
            return Files.deleteIfExists(filePath);
        } catch (IOException e) {
            return false;
        }
    }

    public String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex) : "";
    }

    public String getFileType(String filename) {
        String extension = getFileExtension(filename).toLowerCase();
        
        if (Arrays.asList(".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp").contains(extension)) {
            return "image";
        } else if (Arrays.asList(".pdf", ".doc", ".docx", ".txt", ".md").contains(extension)) {
            return "document";
        } else if (Arrays.asList(".xls", ".xlsx").contains(extension)) {
            return "spreadsheet";
        } else if (Arrays.asList(".ppt", ".pptx").contains(extension)) {
            return "presentation";
        } else if (Arrays.asList(".zip", ".rar", ".7z").contains(extension)) {
            return "archive";
        } else if (Arrays.asList(".mp3", ".wav").contains(extension)) {
            return "audio";
        } else if (Arrays.asList(".mp4", ".avi", ".mov", ".wmv").contains(extension)) {
            return "video";
        } else {
            return "other";
        }
    }

    public long getFileSize(String fileUrl) {
        if (fileUrl == null || !fileUrl.startsWith(uploadUrl)) {
            return 0;
        }

        try {
            String relativePath = fileUrl.substring(uploadUrl.length());
            Path filePath = Paths.get(uploadPath, relativePath);
            return Files.size(filePath);
        } catch (IOException e) {
            return 0;
        }
    }

    public String generateThumbnail(String imageUrl) {
        // 这里可以实现图片缩略图生成逻辑
        // 简单实现：如果是图片文件，返回原图片URL作为缩略图
        String extension = getFileExtension(imageUrl).toLowerCase();
        if (Arrays.asList(".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp").contains(extension)) {
            return imageUrl;
        }
        return null;
    }
}