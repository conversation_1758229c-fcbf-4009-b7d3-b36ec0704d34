package com.aquaculture.management.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;

@Data
@Entity
@Table(name = "document")
@TableName("document")
@EqualsAndHashCode(callSuper = true)
public class Document extends BaseEntity {

    @NotBlank(message = "文档标题不能为空")
    private String title;

    @NotBlank(message = "文档类型不能为空")
    private String type;

    private String category;

    private String content;

    private String summary;

    private String filePath;

    private String fileType;

    private Long fileSize;

    private String tags;

    private Integer viewCount;

    private Integer downloadCount;

    private Integer status;

    private String keywords;

    private String author;

    private String version;

    private String language;
}