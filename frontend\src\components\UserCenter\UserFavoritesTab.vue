<template>
  <div class="user-favorites">
    <div class="favorites-header">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-select v-model="selectedType" placeholder="资源类型" clearable @change="loadFavorites">
            <el-option label="全部" value="" />
            <el-option label="文化资源" value="cultural_resource" />
            <el-option label="文档资料" value="document" />
            <el-option label="活动" value="activity" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <span class="total-count">共 {{ pagination.total }} 个收藏</span>
        </el-col>
      </el-row>
    </div>

    <div class="favorites-content">
      <el-row :gutter="20" v-loading="loading">
        <el-col :span="8" v-for="favorite in favoriteList" :key="favorite.id">
          <el-card class="favorite-card" shadow="hover" @click="viewResource(favorite)">
            <template #header>
              <div class="card-header">
                <span class="resource-title">{{ favorite.resourceTitle }}</span>
                <el-button
                  v-if="isCurrentUser"
                  class="button"
                  text
                  type="danger"
                  @click.stop="removeFavorite(favorite)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </template>
            
            <div class="favorite-info">
              <el-tag size="small" :type="getTypeColor(favorite.resourceType)">
                {{ getTypeName(favorite.resourceType) }}
              </el-tag>
              <p class="favorite-time">{{ formatTime(favorite.createTime) }}</p>
              <div v-if="favorite.tags" class="favorite-tags">
                <el-tag
                  v-for="tag in getTags(favorite.tags)"
                  :key="tag"
                  size="small"
                  effect="plain"
                  style="margin-right: 5px;"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <div v-if="favoriteList.length === 0 && !loading" class="empty-state">
        <el-empty description="暂无收藏内容">
          <el-button type="primary" @click="$router.push('/cultural-resources')">
            去发现好内容
          </el-button>
        </el-empty>
      </div>

      <div class="pagination" v-if="pagination.total > 0">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[12, 24, 48]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadFavorites"
          @current-change="loadFavorites"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

const props = defineProps({
  userId: {
    type: Number,
    default: null
  },
  isCurrentUser: {
    type: Boolean,
    default: true
  }
})

const router = useRouter()
const loading = ref(false)
const selectedType = ref('')

const pagination = reactive({
  current: 1,
  size: 12,
  total: 0
})

const favoriteList = ref([])

const loadFavorites = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      resourceType: selectedType.value
    }
    
    if (props.userId) {
      params.userId = props.userId
    }
    
    const response = await request.get('/user-center/favorites', { params })
    favoriteList.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载收藏列表失败')
  } finally {
    loading.value = false
  }
}

const removeFavorite = async (favorite) => {
  try {
    await ElMessageBox.confirm('确定要取消收藏吗？', '确认操作', {
      type: 'warning'
    })
    
    await request.delete('/user-center/favorites', {
      params: {
        resourceType: favorite.resourceType,
        resourceId: favorite.resourceId
      }
    })
    
    ElMessage.success('取消收藏成功')
    loadFavorites()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消收藏失败')
    }
  }
}

const viewResource = (favorite) => {
  switch (favorite.resourceType) {
    case 'cultural_resource':
      router.push(`/cultural-resources/${favorite.resourceId}`)
      break
    case 'document':
      router.push(`/documents/${favorite.resourceId}`)
      break
    case 'activity':
      router.push(`/activities/${favorite.resourceId}`)
      break
    default:
      ElMessage.info('资源类型未知')
  }
}

const getTypeName = (type) => {
  const typeMap = {
    'cultural_resource': '文化资源',
    'document': '文档资料',
    'activity': '活动'
  }
  return typeMap[type] || type
}

const getTypeColor = (type) => {
  const colorMap = {
    'cultural_resource': 'primary',
    'document': 'success',
    'activity': 'warning'
  }
  return colorMap[type] || 'info'
}

const getTags = (tags) => {
  return tags ? tags.split(',').map(t => t.trim()).filter(t => t) : []
}

const formatTime = (time) => {
  if (!time) return ''
  try {
    return formatDistanceToNow(new Date(time), {
      addSuffix: true,
      locale: zhCN
    })
  } catch (error) {
    return time
  }
}

onMounted(() => {
  loadFavorites()
})
</script>

<style scoped>
.user-favorites {
  padding: 20px 0;
}

.favorites-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.total-count {
  color: #666;
  font-size: 14px;
  line-height: 32px;
}

.favorites-content {
  min-height: 400px;
}

.favorite-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: transform 0.2s;
}

.favorite-card:hover {
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.resource-title {
  font-weight: 500;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 10px;
}

.favorite-info {
  padding: 10px 0;
}

.favorite-time {
  color: #999;
  font-size: 12px;
  margin: 8px 0;
}

.favorite-tags {
  margin-top: 10px;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.pagination {
  margin-top: 30px;
  text-align: center;
}

@media (max-width: 768px) {
  .favorites-content :deep(.el-col-8) {
    width: 100%;
  }
}
</style>