-- 创建数据库
CREATE DATABASE IF NOT EXISTS aquaculture_management 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE aquaculture_management;

-- 用户表
CREATE TABLE IF NOT EXISTS sys_user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    avatar VARCHAR(500) COMMENT '头像URL',
    status INT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    role_ids VARCHAR(100) DEFAULT '4' COMMENT '角色ID列表，逗号分隔',
    dept_id VARCHAR(50) COMMENT '部门ID',
    remark TEXT COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_by VARCHAR(50) COMMENT '更新人',
    deleted INT DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
    INDEX idx_username (username),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 文化资源表
CREATE TABLE IF NOT EXISTS cultural_resource (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '资源ID',
    name VARCHAR(200) NOT NULL COMMENT '资源名称',
    type VARCHAR(50) NOT NULL COMMENT '资源类型',
    category VARCHAR(100) COMMENT '资源分类',
    description TEXT COMMENT '资源描述',
    region VARCHAR(100) COMMENT '地区',
    period VARCHAR(100) COMMENT '历史时期',
    source VARCHAR(200) COMMENT '资源来源',
    file_path VARCHAR(500) COMMENT '文件路径',
    file_type VARCHAR(50) COMMENT '文件类型',
    file_size BIGINT COMMENT '文件大小（字节）',
    thumbnail_path VARCHAR(500) COMMENT '缩略图路径',
    tags VARCHAR(500) COMMENT '标签，逗号分隔',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    status INT DEFAULT 1 COMMENT '状态：0-禁用，1-启用，2-待审核',
    keywords VARCHAR(500) COMMENT '关键词',
    author VARCHAR(100) COMMENT '作者',
    collector VARCHAR(100) COMMENT '收集者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_by VARCHAR(50) COMMENT '更新人',
    deleted INT DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
    INDEX idx_type (type),
    INDEX idx_category (category),
    INDEX idx_region (region),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time),
    FULLTEXT idx_search (name, description, keywords)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文化资源表';

-- 文档资料表
CREATE TABLE IF NOT EXISTS document (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '文档ID',
    title VARCHAR(200) NOT NULL COMMENT '文档标题',
    type VARCHAR(50) NOT NULL COMMENT '文档类型',
    category VARCHAR(100) COMMENT '文档分类',
    content LONGTEXT COMMENT '文档内容',
    summary TEXT COMMENT '文档摘要',
    file_path VARCHAR(500) COMMENT '文件路径',
    file_type VARCHAR(50) COMMENT '文件类型',
    file_size BIGINT COMMENT '文件大小（字节）',
    tags VARCHAR(500) COMMENT '标签，逗号分隔',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    status INT DEFAULT 1 COMMENT '状态：0-禁用，1-启用，2-待审核',
    keywords VARCHAR(500) COMMENT '关键词',
    author VARCHAR(100) COMMENT '作者',
    version VARCHAR(20) DEFAULT '1.0' COMMENT '版本号',
    language VARCHAR(20) DEFAULT 'zh-CN' COMMENT '语言',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_by VARCHAR(50) COMMENT '更新人',
    deleted INT DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
    INDEX idx_type (type),
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time),
    FULLTEXT idx_search (title, content, keywords)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档资料表';

-- 活动表
CREATE TABLE IF NOT EXISTS activity (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '活动ID',
    name VARCHAR(200) NOT NULL COMMENT '活动名称',
    type VARCHAR(50) NOT NULL COMMENT '活动类型',
    description TEXT COMMENT '活动描述',
    location VARCHAR(200) COMMENT '活动地点',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    organizer VARCHAR(100) COMMENT '主办方',
    contact VARCHAR(100) COMMENT '联系人',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '联系邮箱',
    max_participants INT DEFAULT 0 COMMENT '最大参与人数',
    current_participants INT DEFAULT 0 COMMENT '当前参与人数',
    requirements TEXT COMMENT '参与要求',
    materials TEXT COMMENT '所需材料',
    status INT DEFAULT 1 COMMENT '状态：0-取消，1-进行中，2-已结束',
    cover_image VARCHAR(500) COMMENT '封面图片',
    tags VARCHAR(500) COMMENT '标签，逗号分隔',
    fee VARCHAR(100) COMMENT '费用说明',
    notes TEXT COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_by VARCHAR(50) COMMENT '更新人',
    deleted INT DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动表';

-- 统计表
CREATE TABLE IF NOT EXISTS statistics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '统计ID',
    statistics_date DATE NOT NULL COMMENT '统计日期',
    type VARCHAR(50) NOT NULL COMMENT '统计类型',
    category VARCHAR(100) COMMENT '统计分类',
    total_count INT DEFAULT 0 COMMENT '总数量',
    new_count INT DEFAULT 0 COMMENT '新增数量',
    active_count INT DEFAULT 0 COMMENT '活跃数量',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    region VARCHAR(100) COMMENT '地区',
    details JSON COMMENT '详细数据（JSON格式）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_by VARCHAR(50) COMMENT '更新人',
    deleted INT DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
    INDEX idx_date_type (statistics_date, type),
    INDEX idx_category (category),
    INDEX idx_region (region),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='统计表';

-- 活动参与表
CREATE TABLE IF NOT EXISTS activity_participant (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    activity_id BIGINT NOT NULL COMMENT '活动ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    participate_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '参与时间',
    status INT DEFAULT 1 COMMENT '状态：0-已取消，1-已参与',
    remark TEXT COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (activity_id) REFERENCES activity(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES sys_user(id) ON DELETE CASCADE,
    UNIQUE KEY uk_activity_user (activity_id, user_id),
    INDEX idx_activity_id (activity_id),
    INDEX idx_user_id (user_id),
    INDEX idx_participate_time (participate_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动参与表';


-- 用户资料表
CREATE TABLE IF NOT EXISTS user_profile (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    nickname VARCHAR(50) COMMENT '昵称',
    bio TEXT COMMENT '个人简介',
    avatar VARCHAR(500) COMMENT '头像URL',
    birthday DATE COMMENT '生日',
    gender VARCHAR(10) COMMENT '性别：male-男，female-女，other-其他',
    location VARCHAR(100) COMMENT '所在地',
    occupation VARCHAR(100) COMMENT '职业',
    interests VARCHAR(500) COMMENT '兴趣爱好，逗号分隔',
    website VARCHAR(200) COMMENT '个人网站',
    social_media VARCHAR(500) COMMENT '社交媒体账号',
    privacy_level INT DEFAULT 1 COMMENT '隐私级别：1-公开，2-好友可见，3-仅自己可见',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_by VARCHAR(50) COMMENT '更新人',
    deleted INT DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
    FOREIGN KEY (user_id) REFERENCES sys_user(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_id (user_id),
    INDEX idx_nickname (nickname),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户资料表';

-- 用户收藏表
CREATE TABLE IF NOT EXISTS user_favorite (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    resource_type VARCHAR(50) NOT NULL COMMENT '资源类型：cultural_resource-文化资源，document-文档，activity-活动',
    resource_id BIGINT NOT NULL COMMENT '资源ID',
    resource_title VARCHAR(200) COMMENT '资源标题',
    tags VARCHAR(500) COMMENT '标签，逗号分隔',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_by VARCHAR(50) COMMENT '更新人',
    deleted INT DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
    FOREIGN KEY (user_id) REFERENCES sys_user(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_resource (user_id, resource_type, resource_id),
    INDEX idx_user_id (user_id),
    INDEX idx_resource_type (resource_type),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏表';

-- 用户关注表
CREATE TABLE IF NOT EXISTS user_follow (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    follower_id BIGINT NOT NULL COMMENT '关注者ID',
    followee_id BIGINT NOT NULL COMMENT '被关注者ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_by VARCHAR(50) COMMENT '更新人',
    deleted INT DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
    FOREIGN KEY (follower_id) REFERENCES sys_user(id) ON DELETE CASCADE,
    FOREIGN KEY (followee_id) REFERENCES sys_user(id) ON DELETE CASCADE,
    UNIQUE KEY uk_follower_followee (follower_id, followee_id),
    INDEX idx_follower_id (follower_id),
    INDEX idx_followee_id (followee_id),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户关注表';

-- 用户活动记录表
CREATE TABLE IF NOT EXISTS user_activity (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    type VARCHAR(50) NOT NULL COMMENT '活动类型：publish_resource-发布资源，favorite-收藏，follow-关注，comment-评论',
    description TEXT COMMENT '活动描述',
    target_type VARCHAR(50) COMMENT '目标类型：cultural_resource-文化资源，document-文档，activity-活动，user-用户',
    target_id BIGINT COMMENT '目标ID',
    target_title VARCHAR(200) COMMENT '目标标题',
    tags VARCHAR(500) COMMENT '标签，逗号分隔',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-正常，deleted-已删除，private-私有',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_by VARCHAR(50) COMMENT '更新人',
    deleted INT DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
    FOREIGN KEY (user_id) REFERENCES sys_user(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_target (target_type, target_id),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户活动记录表';

-- 内容审核表
CREATE TABLE IF NOT EXISTS content_audit (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    content_type VARCHAR(50) NOT NULL COMMENT '内容类型：cultural_resource-文化资源，document-文档，activity-活动，comment-评论',
    content_id BIGINT NOT NULL COMMENT '内容ID',
    content_title VARCHAR(200) COMMENT '内容标题',
    submit_user_id BIGINT COMMENT '提交用户ID',
    submit_user_name VARCHAR(50) COMMENT '提交用户名',
    audit_status INT NOT NULL DEFAULT 0 COMMENT '审核状态：0-待审核，1-审核通过，2-审核拒绝',
    audit_user_id BIGINT COMMENT '审核用户ID',
    audit_user_name VARCHAR(50) COMMENT '审核用户名',
    audit_time DATETIME COMMENT '审核时间',
    audit_reason TEXT COMMENT '审核意见',
    submit_time DATETIME COMMENT '提交时间',
    priority INT NOT NULL DEFAULT 2 COMMENT '优先级：1-低，2-中，3-高，4-紧急',
    content_summary TEXT COMMENT '内容摘要',
    risk_level INT COMMENT '风险等级：1-低风险，2-中风险，3-高风险',
    keywords VARCHAR(500) COMMENT '关键词',
    auto_audit_score DOUBLE COMMENT '自动审核评分0-100',
    manual_required BOOLEAN DEFAULT TRUE COMMENT '是否需要人工审核',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_by VARCHAR(50) COMMENT '更新人',
    deleted INT DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
    INDEX idx_content (content_type, content_id),
    INDEX idx_audit_status (audit_status),
    INDEX idx_submit_user (submit_user_id),
    INDEX idx_audit_user (audit_user_id),
    INDEX idx_priority (priority),
    INDEX idx_submit_time (submit_time),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='内容审核表';

-- 评论表
CREATE TABLE IF NOT EXISTS comment (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    content TEXT NOT NULL COMMENT '评论内容',
    target_type VARCHAR(50) NOT NULL COMMENT '目标类型：cultural_resource-文化资源，document-文档，activity-活动',
    target_id BIGINT NOT NULL COMMENT '目标ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    user_name VARCHAR(50) COMMENT '用户名',
    user_avatar VARCHAR(500) COMMENT '用户头像',
    parent_id BIGINT COMMENT '父评论ID，用于回复',
    reply_to_user_id BIGINT COMMENT '回复的用户ID',
    reply_to_user_name VARCHAR(50) COMMENT '回复的用户名',
    like_count INT NOT NULL DEFAULT 0 COMMENT '点赞数',
    reply_count INT NOT NULL DEFAULT 0 COMMENT '回复数',
    status INT NOT NULL DEFAULT 1 COMMENT '状态：0-隐藏，1-正常，2-待审核',
    ip_address VARCHAR(64) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    is_pinned BOOLEAN DEFAULT FALSE COMMENT '是否置顶',
    audit_status INT DEFAULT 1 COMMENT '审核状态：0-待审核，1-审核通过，2-审核拒绝',
    audit_time DATETIME COMMENT '审核时间',
    audit_reason TEXT COMMENT '审核原因',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_by VARCHAR(50) COMMENT '更新人',
    deleted INT DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
    FOREIGN KEY (user_id) REFERENCES sys_user(id) ON DELETE CASCADE,
    INDEX idx_target (target_type, target_id),
    INDEX idx_user_id (user_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_status (status),
    INDEX idx_audit_status (audit_status),
    INDEX idx_create_time (create_time),
    FULLTEXT idx_content (content)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';

-- 评论点赞表
CREATE TABLE IF NOT EXISTS comment_like (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    comment_id BIGINT NOT NULL COMMENT '评论ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    like_type INT NOT NULL DEFAULT 1 COMMENT '点赞类型：1-点赞，-1-点踩',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_by VARCHAR(50) COMMENT '更新人',
    deleted INT DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
    FOREIGN KEY (comment_id) REFERENCES comment(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES sys_user(id) ON DELETE CASCADE,
    UNIQUE KEY uk_comment_user (comment_id, user_id),
    INDEX idx_comment_id (comment_id),
    INDEX idx_user_id (user_id),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论点赞表';

-- 系统日志表
CREATE TABLE IF NOT EXISTS system_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    user_id BIGINT COMMENT '用户ID',
    username VARCHAR(50) COMMENT '用户名',
    operation VARCHAR(100) NOT NULL COMMENT '操作类型',
    method VARCHAR(200) COMMENT '请求方法',
    params TEXT COMMENT '请求参数',
    time BIGINT COMMENT '执行时长（毫秒）',
    ip VARCHAR(64) COMMENT 'IP地址',
    location VARCHAR(100) COMMENT '操作地点',
    user_agent VARCHAR(500) COMMENT '用户代理',
    status INT DEFAULT 1 COMMENT '状态：0-失败，1-成功',
    error_msg TEXT COMMENT '错误信息',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_id (user_id),
    INDEX idx_operation (operation),
    INDEX idx_create_time (create_time),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统日志表';