package com.aquaculture.management.controller;

import com.aquaculture.management.config.LogAspect;
import com.aquaculture.management.entity.UserFavorite;
import com.aquaculture.management.entity.UserFollow;
import com.aquaculture.management.entity.UserProfile;
import com.aquaculture.management.service.UserCenterService;
import com.aquaculture.management.utils.Result;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/user-center")
@RequiredArgsConstructor
@CrossOrigin
public class UserCenterController {

    private final UserCenterService userCenterService;

    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()) {
            // 这里简化处理，实际应该从JWT中获取用户ID
            return 1L; // 临时返回固定值，实际应该解析JWT获取
        }
        throw new RuntimeException("用户未登录");
    }

    @GetMapping("/profile")
    @LogAspect.SystemLogAnnotation("获取用户资料")
    public Result<UserProfile> getUserProfile(@RequestParam(required = false) Long userId) {
        try {
            Long targetUserId = userId != null ? userId : getCurrentUserId();
            UserProfile profile = userCenterService.getUserProfile(targetUserId);
            return Result.success(profile);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PutMapping("/profile")
    @LogAspect.SystemLogAnnotation("更新用户资料")
    public Result<Void> updateUserProfile(@RequestBody @Validated UserProfile userProfile) {
        try {
            userProfile.setUserId(getCurrentUserId());
            userCenterService.updateUserProfile(userProfile);
            return Result.success("更新成功");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("/change-password")
    @LogAspect.SystemLogAnnotation("修改密码")
    public Result<Void> changePassword(@RequestBody com.aquaculture.management.dto.ChangePasswordDTO changePasswordDTO) {
        try {
            // 验证两次密码是否一致
            if (!changePasswordDTO.getNewPassword().equals(changePasswordDTO.getConfirmPassword())) {
                return Result.error("两次输入的新密码不一致");
            }

            // 验证新密码强度
            com.aquaculture.management.utils.PasswordUtil.PasswordValidationResult validationResult = 
                com.aquaculture.management.utils.PasswordUtil.validatePassword(changePasswordDTO.getNewPassword());
            if (!validationResult.isValid()) {
                return Result.error("新密码不符合要求：" + validationResult.getErrorMessage());
            }

            userCenterService.changePassword(getCurrentUserId(), changePasswordDTO.getOldPassword(), changePasswordDTO.getNewPassword());
            return Result.success("密码修改成功");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/favorites")
    @LogAspect.SystemLogAnnotation("获取用户收藏")
    public Result<IPage<UserFavorite>> getUserFavorites(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String resourceType,
            @RequestParam(required = false) Long userId) {
        try {
            Long targetUserId = userId != null ? userId : getCurrentUserId();
            IPage<UserFavorite> favorites = userCenterService.getUserFavorites(targetUserId, current, size, resourceType);
            return Result.success(favorites);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("/favorites")
    @LogAspect.SystemLogAnnotation("添加收藏")
    public Result<Void> addFavorite(
            @RequestParam String resourceType,
            @RequestParam Long resourceId,
            @RequestParam String resourceTitle) {
        try {
            boolean success = userCenterService.addFavorite(getCurrentUserId(), resourceType, resourceId, resourceTitle);
            if (success) {
                return Result.success("收藏成功");
            } else {
                return Result.error("已经收藏过了");
            }
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @DeleteMapping("/favorites")
    @LogAspect.SystemLogAnnotation("取消收藏")
    public Result<Void> removeFavorite(@RequestParam String resourceType, @RequestParam Long resourceId) {
        try {
            userCenterService.removeFavorite(getCurrentUserId(), resourceType, resourceId);
            return Result.success("取消收藏成功");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/favorites/check")
    public Result<Boolean> checkFavorite(@RequestParam String resourceType, @RequestParam Long resourceId) {
        try {
            boolean favorited = userCenterService.isResourceFavorited(getCurrentUserId(), resourceType, resourceId);
            return Result.success(favorited);
        } catch (Exception e) {
            return Result.success(false);
        }
    }

    @GetMapping("/followers")
    @LogAspect.SystemLogAnnotation("获取粉丝列表")
    public Result<IPage<UserFollow>> getUserFollowers(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) Long userId) {
        try {
            Long targetUserId = userId != null ? userId : getCurrentUserId();
            IPage<UserFollow> followers = userCenterService.getUserFollowers(targetUserId, current, size);
            return Result.success(followers);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/following")
    @LogAspect.SystemLogAnnotation("获取关注列表")
    public Result<IPage<UserFollow>> getUserFollowing(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) Long userId) {
        try {
            Long targetUserId = userId != null ? userId : getCurrentUserId();
            IPage<UserFollow> following = userCenterService.getUserFollowing(targetUserId, current, size);
            return Result.success(following);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("/follow")
    @LogAspect.SystemLogAnnotation("关注用户")
    public Result<Void> followUser(@RequestParam Long followeeId) {
        try {
            boolean success = userCenterService.followUser(getCurrentUserId(), followeeId);
            if (success) {
                return Result.success("关注成功");
            } else {
                return Result.error("已经关注过了");
            }
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @DeleteMapping("/follow")
    @LogAspect.SystemLogAnnotation("取消关注")
    public Result<Void> unfollowUser(@RequestParam Long followeeId) {
        try {
            userCenterService.unfollowUser(getCurrentUserId(), followeeId);
            return Result.success("取消关注成功");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/follow/check")
    public Result<Boolean> checkFollow(@RequestParam Long followeeId) {
        try {
            boolean followed = userCenterService.isUserFollowed(getCurrentUserId(), followeeId);
            return Result.success(followed);
        } catch (Exception e) {
            return Result.success(false);
        }
    }

    @GetMapping("/statistics")
    @LogAspect.SystemLogAnnotation("获取用户统计")
    public Result<Map<String, Object>> getUserStatistics(@RequestParam(required = false) Long userId) {
        try {
            Long targetUserId = userId != null ? userId : getCurrentUserId();
            Map<String, Object> statistics = userCenterService.getUserStatistics(targetUserId);
            return Result.success(statistics);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
}