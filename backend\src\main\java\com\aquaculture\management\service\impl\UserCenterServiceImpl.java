package com.aquaculture.management.service.impl;

import com.aquaculture.management.entity.User;
import com.aquaculture.management.entity.UserFavorite;
import com.aquaculture.management.entity.UserFollow;
import com.aquaculture.management.entity.UserProfile;
import com.aquaculture.management.mapper.UserFavoriteMapper;
import com.aquaculture.management.mapper.UserFollowMapper;
import com.aquaculture.management.mapper.UserProfileMapper;
import com.aquaculture.management.service.UserCenterService;
import com.aquaculture.management.service.UserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class UserCenterServiceImpl implements UserCenterService {

    private final UserProfileMapper userProfileMapper;
    private final UserFavoriteMapper userFavoriteMapper;
    private final UserFollowMapper userFollowMapper;
    private final UserService userService;
    private final PasswordEncoder passwordEncoder;

    @Override
    public UserProfile getUserProfile(Long userId) {
        LambdaQueryWrapper<UserProfile> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserProfile::getUserId, userId);
        UserProfile profile = userProfileMapper.selectOne(wrapper);
        
        if (profile == null) {
            // 如果用户档案不存在，创建默认档案
            profile = new UserProfile();
            profile.setUserId(userId);
            profile.setPrivacyLevel(1);
            profile.setFollowersCount(0);
            profile.setFollowingCount(0);
            profile.setResourceCount(0);
            profile.setFavoriteCount(0);
            userProfileMapper.insert(profile);
        }
        
        return profile;
    }

    @Override
    public boolean updateUserProfile(UserProfile userProfile) {
        LambdaQueryWrapper<UserProfile> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserProfile::getUserId, userProfile.getUserId());
        UserProfile existing = userProfileMapper.selectOne(wrapper);
        
        if (existing != null) {
            userProfile.setId(existing.getId());
            return userProfileMapper.updateById(userProfile) > 0;
        } else {
            return userProfileMapper.insert(userProfile) > 0;
        }
    }

    @Override
    public boolean createUserProfile(UserProfile userProfile) {
        return userProfileMapper.insert(userProfile) > 0;
    }

    @Override
    @Transactional
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        User user = userService.getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new RuntimeException("原密码错误");
        }
        
        user.setPassword(passwordEncoder.encode(newPassword));
        return userService.updateById(user);
    }

    @Override
    public IPage<UserFavorite> getUserFavorites(Long userId, int current, int size, String resourceType) {
        Page<UserFavorite> page = new Page<>(current, size);
        LambdaQueryWrapper<UserFavorite> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserFavorite::getUserId, userId);
        
        if (resourceType != null && !resourceType.isEmpty()) {
            wrapper.eq(UserFavorite::getResourceType, resourceType);
        }
        
        wrapper.orderByDesc(UserFavorite::getCreateTime);
        return userFavoriteMapper.selectPage(page, wrapper);
    }

    @Override
    @Transactional
    public boolean addFavorite(Long userId, String resourceType, Long resourceId, String resourceTitle) {
        // 检查是否已收藏
        if (isResourceFavorited(userId, resourceType, resourceId)) {
            return false;
        }
        
        UserFavorite favorite = new UserFavorite();
        favorite.setUserId(userId);
        favorite.setResourceType(resourceType);
        favorite.setResourceId(resourceId);
        favorite.setResourceTitle(resourceTitle);
        
        boolean result = userFavoriteMapper.insert(favorite) > 0;
        
        if (result) {
            // 更新用户收藏数量
            updateUserFavoriteCount(userId);
        }
        
        return result;
    }

    @Override
    @Transactional
    public boolean removeFavorite(Long userId, String resourceType, Long resourceId) {
        LambdaQueryWrapper<UserFavorite> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserFavorite::getUserId, userId)
               .eq(UserFavorite::getResourceType, resourceType)
               .eq(UserFavorite::getResourceId, resourceId);
        
        boolean result = userFavoriteMapper.delete(wrapper) > 0;
        
        if (result) {
            // 更新用户收藏数量
            updateUserFavoriteCount(userId);
        }
        
        return result;
    }

    @Override
    public boolean isResourceFavorited(Long userId, String resourceType, Long resourceId) {
        LambdaQueryWrapper<UserFavorite> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserFavorite::getUserId, userId)
               .eq(UserFavorite::getResourceType, resourceType)
               .eq(UserFavorite::getResourceId, resourceId);
        return userFavoriteMapper.selectCount(wrapper) > 0;
    }

    @Override
    public IPage<UserFollow> getUserFollowers(Long userId, int current, int size) {
        Page<UserFollow> page = new Page<>(current, size);
        LambdaQueryWrapper<UserFollow> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserFollow::getFolloweeId, userId)
               .eq(UserFollow::getStatus, 1)
               .orderByDesc(UserFollow::getCreateTime);
        return userFollowMapper.selectPage(page, wrapper);
    }

    @Override
    public IPage<UserFollow> getUserFollowing(Long userId, int current, int size) {
        Page<UserFollow> page = new Page<>(current, size);
        LambdaQueryWrapper<UserFollow> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserFollow::getFollowerId, userId)
               .eq(UserFollow::getStatus, 1)
               .orderByDesc(UserFollow::getCreateTime);
        return userFollowMapper.selectPage(page, wrapper);
    }

    @Override
    @Transactional
    public boolean followUser(Long followerId, Long followeeId) {
        if (followerId.equals(followeeId)) {
            throw new RuntimeException("不能关注自己");
        }
        
        // 检查是否已关注
        if (isUserFollowed(followerId, followeeId)) {
            return false;
        }
        
        UserFollow follow = new UserFollow();
        follow.setFollowerId(followerId);
        follow.setFolloweeId(followeeId);
        follow.setStatus(1);
        
        boolean result = userFollowMapper.insert(follow) > 0;
        
        if (result) {
            // 更新关注数量
            updateUserFollowCount(followerId, followeeId);
        }
        
        return result;
    }

    @Override
    @Transactional
    public boolean unfollowUser(Long followerId, Long followeeId) {
        LambdaQueryWrapper<UserFollow> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserFollow::getFollowerId, followerId)
               .eq(UserFollow::getFolloweeId, followeeId);
        
        boolean result = userFollowMapper.delete(wrapper) > 0;
        
        if (result) {
            // 更新关注数量
            updateUserFollowCount(followerId, followeeId);
        }
        
        return result;
    }

    @Override
    public boolean isUserFollowed(Long followerId, Long followeeId) {
        LambdaQueryWrapper<UserFollow> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserFollow::getFollowerId, followerId)
               .eq(UserFollow::getFolloweeId, followeeId)
               .eq(UserFollow::getStatus, 1);
        return userFollowMapper.selectCount(wrapper) > 0;
    }

    @Override
    public Map<String, Object> getUserStatistics(Long userId) {
        Map<String, Object> stats = new HashMap<>();
        
        UserProfile profile = getUserProfile(userId);
        stats.put("followersCount", profile.getFollowersCount());
        stats.put("followingCount", profile.getFollowingCount());
        stats.put("favoriteCount", profile.getFavoriteCount());
        stats.put("resourceCount", profile.getResourceCount());
        
        // 获取收藏分类统计
        LambdaQueryWrapper<UserFavorite> favoriteWrapper = new LambdaQueryWrapper<>();
        favoriteWrapper.eq(UserFavorite::getUserId, userId);
        Long totalFavorites = userFavoriteMapper.selectCount(favoriteWrapper);
        stats.put("totalFavorites", totalFavorites);
        
        return stats;
    }

    private void updateUserFavoriteCount(Long userId) {
        LambdaQueryWrapper<UserFavorite> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserFavorite::getUserId, userId);
        Long count = userFavoriteMapper.selectCount(wrapper);
        
        UserProfile profile = getUserProfile(userId);
        profile.setFavoriteCount(count.intValue());
        updateUserProfile(profile);
    }

    private void updateUserFollowCount(Long followerId, Long followeeId) {
        // 更新关注者的关注数
        LambdaQueryWrapper<UserFollow> followingWrapper = new LambdaQueryWrapper<>();
        followingWrapper.eq(UserFollow::getFollowerId, followerId).eq(UserFollow::getStatus, 1);
        Long followingCount = userFollowMapper.selectCount(followingWrapper);
        
        UserProfile followerProfile = getUserProfile(followerId);
        followerProfile.setFollowingCount(followingCount.intValue());
        updateUserProfile(followerProfile);
        
        // 更新被关注者的粉丝数
        LambdaQueryWrapper<UserFollow> followersWrapper = new LambdaQueryWrapper<>();
        followersWrapper.eq(UserFollow::getFolloweeId, followeeId).eq(UserFollow::getStatus, 1);
        Long followersCount = userFollowMapper.selectCount(followersWrapper);
        
        UserProfile followeeProfile = getUserProfile(followeeId);
        followeeProfile.setFollowersCount(followersCount.intValue());
        updateUserProfile(followeeProfile);
    }
}