package com.aquaculture.management.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "system_log")
@TableName("system_log")
@EqualsAndHashCode(callSuper = true)
public class SystemLog extends BaseEntity {

    private Long userId;

    private String username;

    private String operation;

    private String method;

    private String params;

    private Long time;

    private String ip;

    private String location;

    private String userAgent;

    private Integer status;

    private String errorMsg;
}