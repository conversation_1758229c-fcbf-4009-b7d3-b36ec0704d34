package com.aquaculture.management.controller;

import com.aquaculture.management.entity.Document;
import com.aquaculture.management.service.DocumentService;
import com.aquaculture.management.utils.Result;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/documents")
@RequiredArgsConstructor
@CrossOrigin
public class DocumentController {

    private final DocumentService documentService;

    @GetMapping
    public Result<IPage<Document>> getDocuments(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String category) {
        try {
            IPage<Document> documents = documentService.getDocuments(current, size, keyword, type, category);
            return Result.success(documents);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public Result<Document> getDocument(@PathVariable Long id) {
        try {
            Document document = documentService.getById(id);
            if (document == null) {
                return Result.error("文档不存在");
            }
            documentService.incrementViewCount(id);
            return Result.success(document);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PostMapping
    public Result<Void> createDocument(@RequestBody @Validated Document document) {
        try {
            documentService.createDocument(document);
            return Result.success("创建成功");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PutMapping("/{id}")
    public Result<Void> updateDocument(@PathVariable Long id, @RequestBody @Validated Document document) {
        try {
            document.setId(id);
            documentService.updateDocument(document);
            return Result.success("更新成功");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    public Result<Void> deleteDocument(@PathVariable Long id) {
        try {
            documentService.deleteDocument(id);
            return Result.success("删除成功");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("/{id}/download")
    public Result<Void> downloadDocument(@PathVariable Long id) {
        try {
            documentService.incrementDownloadCount(id);
            return Result.success("下载统计已更新");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/recent")
    public Result<List<Document>> getRecentDocuments(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<Document> documents = documentService.getRecentDocuments(limit);
            return Result.success(documents);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
}