package com.aquaculture.management.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "user_favorite")
@TableName("user_favorite")
@EqualsAndHashCode(callSuper = true)
public class UserFavorite extends BaseEntity {

    private Long userId;

    private String resourceType;

    private Long resourceId;

    private String resourceTitle;

    private String category;

    private String tags;
}