package com.aquaculture.management.service;

import com.aquaculture.management.dto.Result;
import com.aquaculture.management.entity.ContentAudit;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

public interface ContentAuditService extends IService<ContentAudit> {

    /**
     * 提交内容审核
     */
    Result<Void> submitForAudit(String contentType, Long contentId, String contentTitle, 
                               String contentSummary, Integer priority);

    /**
     * 分页查询审核记录
     */
    Result<IPage<ContentAudit>> getAuditPage(Integer current, Integer size, 
                                           String contentType, Integer auditStatus, 
                                           Integer priority, Long submitUserId);

    /**
     * 审核通过
     */
    Result<Void> approveAudit(Long auditId, String auditReason);

    /**
     * 审核拒绝
     */
    Result<Void> rejectAudit(Long auditId, String auditReason);

    /**
     * 批量审核
     */
    Result<Void> batchAudit(List<Long> auditIds, Integer auditStatus, String auditReason);

    /**
     * 获取审核统计
     */
    Result<Map<String, Object>> getAuditStatistics();

    /**
     * 自动审核
     */
    Result<Void> autoAudit(Long auditId);

    /**
     * 获取待审核数量
     */
    Result<Long> getPendingCount();

    /**
     * 获取我的审核记录
     */
    Result<IPage<ContentAudit>> getMyAuditRecords(Integer current, Integer size, Integer auditStatus);
}