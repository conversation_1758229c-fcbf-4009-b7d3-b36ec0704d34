package com.aquaculture.management.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "content_audit")
@TableName("content_audit")
public class ContentAudit extends BaseEntity {

    @Column(name = "content_type", nullable = false, length = 50)
    private String contentType; // cultural_resource, document, activity, comment

    @Column(name = "content_id", nullable = false)
    private Long contentId;

    @Column(name = "content_title", length = 200)
    private String contentTitle;

    @Column(name = "submit_user_id")
    private Long submitUserId;

    @Column(name = "submit_user_name", length = 50)
    private String submitUserName;

    @Column(name = "audit_status", nullable = false)
    private Integer auditStatus; // 0-待审核，1-审核通过，2-审核拒绝

    @Column(name = "audit_user_id")
    private Long auditUserId;

    @Column(name = "audit_user_name", length = 50)
    private String auditUserName;

    @Column(name = "audit_time")
    private LocalDateTime auditTime;

    @Column(name = "audit_reason", columnDefinition = "TEXT")
    private String auditReason;

    @Column(name = "submit_time")
    private LocalDateTime submitTime;

    @Column(name = "priority", nullable = false)
    private Integer priority; // 1-低，2-中，3-高，4-紧急

    @Column(name = "content_summary", columnDefinition = "TEXT")
    private String contentSummary;

    @Column(name = "risk_level")
    private Integer riskLevel; // 1-低风险，2-中风险，3-高风险

    @Column(name = "keywords", length = 500)
    private String keywords;

    @Column(name = "auto_audit_score")
    private Double autoAuditScore; // 自动审核评分 0-100

    @Column(name = "manual_required")
    private Boolean manualRequired; // 是否需要人工审核
}