package com.aquaculture.management.service.impl;

import com.aquaculture.management.entity.Activity;
import com.aquaculture.management.mapper.ActivityMapper;
import com.aquaculture.management.service.ActivityService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class ActivityServiceImpl extends ServiceImpl<ActivityMapper, Activity> implements ActivityService {

    @Override
    public IPage<Activity> getActivities(int current, int size, String keyword, String type, Integer status) {
        Page<Activity> page = new Page<>(current, size);
        LambdaQueryWrapper<Activity> wrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(keyword)) {
            wrapper.like(Activity::getName, keyword)
                   .or()
                   .like(Activity::getDescription, keyword)
                   .or()
                   .like(Activity::getLocation, keyword);
        }

        if (StringUtils.hasText(type)) {
            wrapper.eq(Activity::getType, type);
        }

        if (status != null) {
            wrapper.eq(Activity::getStatus, status);
        }

        wrapper.orderByDesc(Activity::getCreateTime);
        return this.page(page, wrapper);
    }

    @Override
    public boolean createActivity(Activity activity) {
        activity.setStatus(1);
        activity.setCurrentParticipants(0);
        return this.save(activity);
    }

    @Override
    public boolean updateActivity(Activity activity) {
        Activity existingActivity = this.getById(activity.getId());
        if (existingActivity == null) {
            throw new RuntimeException("活动不存在");
        }
        return this.updateById(activity);
    }

    @Override
    public boolean deleteActivity(Long id) {
        return this.removeById(id);
    }

    @Override
    public List<Activity> getUpcomingActivities(int limit) {
        LambdaQueryWrapper<Activity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Activity::getStatus, 1)
               .gt(Activity::getStartTime, LocalDateTime.now())
               .orderByAsc(Activity::getStartTime)
               .last("LIMIT " + limit);
        return this.list(wrapper);
    }

    @Override
    public List<Activity> getOngoingActivities() {
        LambdaQueryWrapper<Activity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Activity::getStatus, 1)
               .le(Activity::getStartTime, LocalDateTime.now())
               .ge(Activity::getEndTime, LocalDateTime.now())
               .orderByAsc(Activity::getEndTime);
        return this.list(wrapper);
    }

    @Override
    public boolean participateActivity(Long activityId) {
        Activity activity = this.getById(activityId);
        if (activity == null) {
            throw new RuntimeException("活动不存在");
        }

        if (activity.getStatus() != 1) {
            throw new RuntimeException("活动已关闭");
        }

        if (activity.getCurrentParticipants() >= activity.getMaxParticipants()) {
            throw new RuntimeException("活动已满员");
        }

        activity.setCurrentParticipants(activity.getCurrentParticipants() + 1);
        return this.updateById(activity);
    }

    @Override
    public boolean cancelParticipation(Long activityId) {
        Activity activity = this.getById(activityId);
        if (activity == null) {
            throw new RuntimeException("活动不存在");
        }

        if (activity.getCurrentParticipants() > 0) {
            activity.setCurrentParticipants(activity.getCurrentParticipants() - 1);
            return this.updateById(activity);
        }

        return true;
    }
}