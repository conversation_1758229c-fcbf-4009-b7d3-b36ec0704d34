package com.aquaculture.management.mapper;

import com.aquaculture.management.entity.Comment;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface CommentMapper extends BaseMapper<Comment> {

    @Select("SELECT " +
            "target_type, " +
            "COUNT(*) as count " +
            "FROM comment " +
            "WHERE deleted = 0 AND status = 1 AND audit_status = 1 " +
            "GROUP BY target_type")
    List<Map<String, Object>> getCommentStatistics();

    @Select("SELECT COUNT(*) FROM comment " +
            "WHERE target_type = #{targetType} AND target_id = #{targetId} " +
            "AND deleted = 0 AND status = 1 AND audit_status = 1")
    Long getCommentCount(@Param("targetType") String targetType, @Param("targetId") Long targetId);
}