package com.aquaculture.management.controller;

import com.aquaculture.management.entity.CulturalResource;
import com.aquaculture.management.service.CulturalResourceService;
import com.aquaculture.management.utils.Result;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/cultural-resources")
@RequiredArgsConstructor
@CrossOrigin
public class CulturalResourceController {

    private final CulturalResourceService culturalResourceService;

    @GetMapping
    public Result<IPage<CulturalResource>> getResources(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String region) {
        try {
            IPage<CulturalResource> resources = culturalResourceService.getResources(current, size, keyword, type, category, region);
            return Result.success(resources);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public Result<CulturalResource> getResource(@PathVariable Long id) {
        try {
            CulturalResource resource = culturalResourceService.getById(id);
            if (resource == null) {
                return Result.error("资源不存在");
            }
            culturalResourceService.incrementViewCount(id);
            return Result.success(resource);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PostMapping
    public Result<Void> createResource(@RequestBody @Validated CulturalResource resource) {
        try {
            culturalResourceService.createResource(resource);
            return Result.success("创建成功");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PutMapping("/{id}")
    public Result<Void> updateResource(@PathVariable Long id, @RequestBody @Validated CulturalResource resource) {
        try {
            resource.setId(id);
            culturalResourceService.updateResource(resource);
            return Result.success("更新成功");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    public Result<Void> deleteResource(@PathVariable Long id) {
        try {
            culturalResourceService.deleteResource(id);
            return Result.success("删除成功");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("/{id}/download")
    public Result<Void> downloadResource(@PathVariable Long id) {
        try {
            culturalResourceService.incrementDownloadCount(id);
            return Result.success("下载统计已更新");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/recommended")
    public Result<List<CulturalResource>> getRecommendedResources(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<CulturalResource> resources = culturalResourceService.getRecommendedResources(limit);
            return Result.success(resources);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/statistics/type")
    public Result<List<Map<String, Object>>> getResourceTypeStatistics() {
        try {
            List<Map<String, Object>> statistics = culturalResourceService.getResourceTypeStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/statistics/region")
    public Result<List<Map<String, Object>>> getResourceRegionStatistics() {
        try {
            List<Map<String, Object>> statistics = culturalResourceService.getResourceRegionStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
}