package com.aquaculture.management.mapper;

import com.aquaculture.management.entity.ContentAudit;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface ContentAuditMapper extends BaseMapper<ContentAudit> {

    @Select("SELECT " +
            "audit_status, " +
            "COUNT(*) as count " +
            "FROM content_audit " +
            "WHERE deleted = 0 " +
            "GROUP BY audit_status")
    List<Map<String, Object>> getAuditStatistics();

    @Select("SELECT " +
            "content_type, " +
            "COUNT(*) as count " +
            "FROM content_audit " +
            "WHERE deleted = 0 AND audit_status = #{status} " +
            "GROUP BY content_type")
    List<Map<String, Object>> getAuditStatisticsByType(@Param("status") Integer status);

    @Select("<script>" +
            "SELECT * FROM content_audit WHERE deleted = 0 " +
            "<if test='contentType != null and contentType != \"\"'>" +
            "AND content_type = #{contentType} " +
            "</if>" +
            "<if test='auditStatus != null'>" +
            "AND audit_status = #{auditStatus} " +
            "</if>" +
            "<if test='priority != null'>" +
            "AND priority = #{priority} " +
            "</if>" +
            "<if test='submitUserId != null'>" +
            "AND submit_user_id = #{submitUserId} " +
            "</if>" +
            "ORDER BY submit_time DESC" +
            "</script>")
    IPage<ContentAudit> selectAuditPage(Page<ContentAudit> page, 
                                       @Param("contentType") String contentType,
                                       @Param("auditStatus") Integer auditStatus,
                                       @Param("priority") Integer priority,
                                       @Param("submitUserId") Long submitUserId);
}