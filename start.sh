#!/bin/bash

echo "========================================"
echo "   水族文化资源管理平台启动脚本"
echo "========================================"
echo

echo "[1] 检查环境..."
if ! command -v java &> /dev/null; then
    echo "错误：未找到Java环境，请先安装JDK 11+"
    exit 1
fi

if ! command -v node &> /dev/null; then
    echo "错误：未找到Node.js环境，请先安装Node.js 16+"
    exit 1
fi

echo "Java环境检查通过"
echo "Node.js环境检查通过"
echo

echo "[2] 启动后端服务..."
cd backend
echo "正在编译后端项目..."
mvn clean compile -q
if [ $? -ne 0 ]; then
    echo "错误：后端编译失败"
    exit 1
fi

echo "正在启动SpringBoot服务..."
nohup mvn spring-boot:run > ../backend.log 2>&1 &
BACKEND_PID=$!
echo "后端服务PID: $BACKEND_PID"
echo "后端服务启动中，请等待..."
sleep 10

cd ../frontend
echo
echo "[3] 启动前端服务..."

if [ ! -d "node_modules" ]; then
    echo "正在安装前端依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "错误：前端依赖安装失败"
        exit 1
    fi
fi

echo "正在启动前端开发服务器..."
nohup npm run dev > ../frontend.log 2>&1 &
FRONTEND_PID=$!
echo "前端服务PID: $FRONTEND_PID"

cd ..

echo
echo "========================================"
echo "服务启动完成！"
echo "========================================"
echo "后端服务：http://localhost:8080"
echo "前端服务：http://localhost:3000"
echo
echo "默认管理员账户："
echo "用户名：admin"
echo "密码：admin123"
echo
echo "服务进程ID："
echo "后端PID: $BACKEND_PID"
echo "前端PID: $FRONTEND_PID"
echo
echo "查看日志："
echo "后端日志: tail -f backend.log"
echo "前端日志: tail -f frontend.log"
echo
echo "停止服务："
echo "kill $BACKEND_PID $FRONTEND_PID"
echo "========================================"