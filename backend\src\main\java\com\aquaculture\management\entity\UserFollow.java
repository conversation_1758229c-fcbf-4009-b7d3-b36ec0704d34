package com.aquaculture.management.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "user_follow")
@TableName("user_follow")
@EqualsAndHashCode(callSuper = true)
public class UserFollow extends BaseEntity {

    private Long followerId;

    private Long followeeId;

    private Integer status;

    private String remark;
}