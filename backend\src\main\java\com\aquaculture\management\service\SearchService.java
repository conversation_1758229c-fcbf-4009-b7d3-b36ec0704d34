package com.aquaculture.management.service;

import com.aquaculture.management.dto.Result;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Map;

public interface SearchService {

    /**
     * 全文搜索
     */
    Result<IPage<Map<String, Object>>> search(String keyword, String type, String region, 
                                            String period, String sortBy, Integer current, Integer size);

    /**
     * 获取搜索建议
     */
    Result<List<String>> getSearchSuggestions(String keyword);

    /**
     * 获取热门搜索关键词
     */
    Result<List<String>> getHotKeywords();

    /**
     * 获取搜索统计
     */
    Result<Map<String, Object>> getSearchStatistics();

    /**
     * 记录搜索日志
     */
    void recordSearchLog(String keyword, String type, Integer resultCount);
}