package com.aquaculture.management.controller;

import com.aquaculture.management.dto.Result;
import com.aquaculture.management.entity.ContentAudit;
import com.aquaculture.management.service.ContentAuditService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@Tag(name = "内容审核管理")
@RestController
@RequestMapping("/content-audit")
@RequiredArgsConstructor
public class ContentAuditController {

    private final ContentAuditService contentAuditService;

    @Operation(summary = "提交内容审核")
    @PostMapping("/submit")
    public Result<Void> submitForAudit(@RequestParam String contentType,
                                      @RequestParam Long contentId,
                                      @RequestParam String contentTitle,
                                      @RequestParam(required = false) String contentSummary,
                                      @RequestParam(required = false) Integer priority) {
        return contentAuditService.submitForAudit(contentType, contentId, contentTitle, contentSummary, priority);
    }

    @Operation(summary = "分页查询审核记录")
    @GetMapping("/page")
    public Result<IPage<ContentAudit>> getAuditPage(@RequestParam(defaultValue = "1") Integer current,
                                                   @RequestParam(defaultValue = "10") Integer size,
                                                   @RequestParam(required = false) String contentType,
                                                   @RequestParam(required = false) Integer auditStatus,
                                                   @RequestParam(required = false) Integer priority,
                                                   @RequestParam(required = false) Long submitUserId) {
        return contentAuditService.getAuditPage(current, size, contentType, auditStatus, priority, submitUserId);
    }

    @Operation(summary = "审核通过")
    @PostMapping("/approve/{auditId}")
    public Result<Void> approveAudit(@PathVariable Long auditId,
                                    @RequestParam(required = false) String auditReason) {
        return contentAuditService.approveAudit(auditId, auditReason);
    }

    @Operation(summary = "审核拒绝")
    @PostMapping("/reject/{auditId}")
    public Result<Void> rejectAudit(@PathVariable Long auditId,
                                   @RequestParam String auditReason) {
        return contentAuditService.rejectAudit(auditId, auditReason);
    }

    @Operation(summary = "批量审核")
    @PostMapping("/batch")
    public Result<Void> batchAudit(@RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<Long> auditIds = (List<Long>) request.get("auditIds");
        Integer auditStatus = (Integer) request.get("auditStatus");
        String auditReason = (String) request.get("auditReason");
        
        return contentAuditService.batchAudit(auditIds, auditStatus, auditReason);
    }

    @Operation(summary = "获取审核统计")
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getAuditStatistics() {
        return contentAuditService.getAuditStatistics();
    }

    @Operation(summary = "自动审核")
    @PostMapping("/auto/{auditId}")
    public Result<Void> autoAudit(@PathVariable Long auditId) {
        return contentAuditService.autoAudit(auditId);
    }

    @Operation(summary = "获取待审核数量")
    @GetMapping("/pending-count")
    public Result<Long> getPendingCount() {
        return contentAuditService.getPendingCount();
    }

    @Operation(summary = "获取我的审核记录")
    @GetMapping("/my-records")
    public Result<IPage<ContentAudit>> getMyAuditRecords(@RequestParam(defaultValue = "1") Integer current,
                                                        @RequestParam(defaultValue = "10") Integer size,
                                                        @RequestParam(required = false) Integer auditStatus) {
        return contentAuditService.getMyAuditRecords(current, size, auditStatus);
    }

    @Operation(summary = "获取审核详情")
    @GetMapping("/{auditId}")
    public Result<ContentAudit> getAuditDetail(@PathVariable Long auditId) {
        ContentAudit audit = contentAuditService.getById(auditId);
        return audit != null ? Result.success(audit) : Result.error("审核记录不存在");
    }
}