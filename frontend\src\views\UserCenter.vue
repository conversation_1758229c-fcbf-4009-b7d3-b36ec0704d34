<template>
  <div class="user-center">
    <div class="user-header">
      <div class="user-avatar">
        <el-avatar :size="100" :src="userProfile.avatar" @click="showAvatarUpload = true">
          <el-icon><User /></el-icon>
        </el-avatar>
        <el-button v-if="isCurrentUser" type="text" @click="showAvatarUpload = true">
          <el-icon><Camera /></el-icon>
          更换头像
        </el-button>
      </div>
      
      <div class="user-info">
        <h2>{{ userProfile.nickname || userInfo?.realName || userInfo?.username }}</h2>
        <p class="user-bio">{{ userProfile.bio || '这个人很懒，什么都没留下...' }}</p>
        
        <div class="user-stats">
          <div class="stat-item">
            <span class="stat-number">{{ statistics.followersCount || 0 }}</span>
            <span class="stat-label">粉丝</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ statistics.followingCount || 0 }}</span>
            <span class="stat-label">关注</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ statistics.favoriteCount || 0 }}</span>
            <span class="stat-label">收藏</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ statistics.resourceCount || 0 }}</span>
            <span class="stat-label">发布</span>
          </div>
        </div>
        
        <div class="user-actions" v-if="!isCurrentUser">
          <el-button 
            :type="isFollowed ? 'default' : 'primary'"
            @click="toggleFollow"
            :loading="followLoading"
          >
            {{ isFollowed ? '已关注' : '关注' }}
          </el-button>
          <el-button>私信</el-button>
        </div>
        
        <div class="user-actions" v-else>
          <el-button type="primary" @click="showEditProfile = true">
            编辑资料
          </el-button>
        </div>
      </div>
    </div>

    <div class="user-content">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="个人资料" name="profile" v-if="isCurrentUser">
          <user-profile-tab :user-profile="userProfile" @update="loadUserProfile" />
        </el-tab-pane>
        
        <el-tab-pane label="收藏" name="favorites">
          <user-favorites-tab :user-id="userId" :is-current-user="isCurrentUser" />
        </el-tab-pane>
        
        <el-tab-pane label="关注" name="following">
          <user-following-tab :user-id="userId" :is-current-user="isCurrentUser" />
        </el-tab-pane>
        
        <el-tab-pane label="粉丝" name="followers">
          <user-followers-tab :user-id="userId" :is-current-user="isCurrentUser" />
        </el-tab-pane>
        
        <el-tab-pane label="动态" name="activities" v-if="isCurrentUser">
          <user-activities-tab :user-id="userId" />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 编辑资料对话框 -->
    <el-dialog v-model="showEditProfile" title="编辑个人资料" width="600px">
      <edit-profile-form 
        :user-profile="userProfile" 
        @success="handleProfileUpdate" 
        @cancel="showEditProfile = false" 
      />
    </el-dialog>

    <!-- 头像上传对话框 -->
    <el-dialog v-model="showAvatarUpload" title="更换头像" width="400px">
      <avatar-upload @success="handleAvatarUpdate" @cancel="showAvatarUpload = false" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/store/user'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'
import UserProfileTab from '@/components/UserCenter/UserProfileTab.vue'
import UserFavoritesTab from '@/components/UserCenter/UserFavoritesTab.vue'
import UserFollowingTab from '@/components/UserCenter/UserFollowingTab.vue'
import UserFollowersTab from '@/components/UserCenter/UserFollowersTab.vue'
import UserActivitiesTab from '@/components/UserCenter/UserActivitiesTab.vue'
import EditProfileForm from '@/components/UserCenter/EditProfileForm.vue'
import AvatarUpload from '@/components/UserCenter/AvatarUpload.vue'

const route = useRoute()
const userStore = useUserStore()

const activeTab = ref('profile')
const showEditProfile = ref(false)
const showAvatarUpload = ref(false)
const followLoading = ref(false)
const isFollowed = ref(false)

const userId = computed(() => {
  return route.params.userId ? parseInt(route.params.userId) : null
})

const isCurrentUser = computed(() => {
  return !userId.value || userId.value === userStore.userInfo?.id
})

const userInfo = ref(null)
const userProfile = ref({})
const statistics = ref({})

const loadUserProfile = async () => {
  try {
    const params = userId.value ? { userId: userId.value } : {}
    const response = await request.get('/user-center/profile', { params })
    userProfile.value = response.data
    
    if (!isCurrentUser.value) {
      // 如果不是当前用户，获取用户基本信息
      const userResponse = await request.get(`/users/${userId.value}`)
      userInfo.value = userResponse.data
    } else {
      userInfo.value = userStore.userInfo
    }
    
    // 加载统计信息
    await loadUserStatistics()
    
    // 检查是否已关注（非当前用户时）
    if (!isCurrentUser.value) {
      await checkFollowStatus()
    }
  } catch (error) {
    ElMessage.error('加载用户资料失败')
  }
}

const loadUserStatistics = async () => {
  try {
    const params = userId.value ? { userId: userId.value } : {}
    const response = await request.get('/user-center/statistics', { params })
    statistics.value = response.data
  } catch (error) {
    console.error('加载统计信息失败', error)
  }
}

const checkFollowStatus = async () => {
  if (!userId.value || isCurrentUser.value) return
  
  try {
    const response = await request.get('/user-center/follow/check', {
      params: { followeeId: userId.value }
    })
    isFollowed.value = response.data
  } catch (error) {
    console.error('检查关注状态失败', error)
  }
}

const toggleFollow = async () => {
  if (!userId.value) return
  
  followLoading.value = true
  try {
    if (isFollowed.value) {
      await request.delete('/user-center/follow', {
        params: { followeeId: userId.value }
      })
      ElMessage.success('取消关注成功')
      isFollowed.value = false
      statistics.value.followersCount--
    } else {
      await request.post('/user-center/follow', null, {
        params: { followeeId: userId.value }
      })
      ElMessage.success('关注成功')
      isFollowed.value = true
      statistics.value.followersCount++
    }
  } catch (error) {
    ElMessage.error(error.response?.data?.message || '操作失败')
  } finally {
    followLoading.value = false
  }
}

const handleTabChange = (tabName) => {
  activeTab.value = tabName
}

const handleProfileUpdate = () => {
  showEditProfile.value = false
  loadUserProfile()
  ElMessage.success('资料更新成功')
}

const handleAvatarUpdate = (avatarUrl) => {
  showAvatarUpload.value = false
  userProfile.value.avatar = avatarUrl
  if (isCurrentUser.value) {
    userStore.userInfo.avatar = avatarUrl
  }
  ElMessage.success('头像更新成功')
}

watch(() => route.params.userId, () => {
  loadUserProfile()
}, { immediate: true })

onMounted(() => {
  if (isCurrentUser.value) {
    activeTab.value = 'profile'
  } else {
    activeTab.value = 'favorites'
  }
})
</script>

<style scoped>
.user-center {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.user-header {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 30px;
}

.user-avatar {
  text-align: center;
}

.user-avatar .el-button {
  margin-top: 10px;
  font-size: 12px;
}

.user-info {
  flex: 1;
}

.user-info h2 {
  margin: 0 0 10px 0;
  font-size: 28px;
  color: #333;
}

.user-bio {
  color: #666;
  margin: 0 0 20px 0;
  font-size: 14px;
  line-height: 1.5;
}

.user-stats {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.user-actions {
  display: flex;
  gap: 10px;
}

.user-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.user-content :deep(.el-tabs__header) {
  margin: 0;
  padding: 0 20px;
  background: #fafafa;
}

.user-content :deep(.el-tab-pane) {
  padding: 20px;
}

@media (max-width: 768px) {
  .user-header {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .user-stats {
    justify-content: center;
  }
}
</style>