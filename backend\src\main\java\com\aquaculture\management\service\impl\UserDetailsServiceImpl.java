package com.aquaculture.management.service.impl;

import com.aquaculture.management.entity.User;
import com.aquaculture.management.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Service
@RequiredArgsConstructor
public class UserDetailsServiceImpl implements UserDetailsService {

    private final UserService userService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        User user = userService.getUserByUsername(username);
        if (user == null) {
            throw new UsernameNotFoundException("用户不存在: " + username);
        }

        return new org.springframework.security.core.userdetails.User(
                user.getUsername(),
                user.getPassword(),
                user.getStatus() == 1,
                true,
                true,
                true,
                getAuthorities(user)
        );
    }

    private Collection<? extends GrantedAuthority> getAuthorities(User user) {
        List<GrantedAuthority> authorities = new ArrayList<>();
        
        // 添加默认角色
        authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
        
        // 根据用户角色添加权限
        if (user.getRoleIds() != null && !user.getRoleIds().isEmpty()) {
            String[] roleIds = user.getRoleIds().split(",");
            for (String roleId : roleIds) {
                switch (roleId.trim()) {
                    case "1":
                        authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
                        authorities.add(new SimpleGrantedAuthority("USER_MANAGE"));
                        authorities.add(new SimpleGrantedAuthority("RESOURCE_MANAGE"));
                        authorities.add(new SimpleGrantedAuthority("DOCUMENT_MANAGE"));
                        authorities.add(new SimpleGrantedAuthority("ACTIVITY_MANAGE"));
                        authorities.add(new SimpleGrantedAuthority("STATISTICS_VIEW"));
                        break;
                    case "2":
                        authorities.add(new SimpleGrantedAuthority("ROLE_MANAGER"));
                        authorities.add(new SimpleGrantedAuthority("RESOURCE_MANAGE"));
                        authorities.add(new SimpleGrantedAuthority("DOCUMENT_MANAGE"));
                        authorities.add(new SimpleGrantedAuthority("ACTIVITY_MANAGE"));
                        authorities.add(new SimpleGrantedAuthority("STATISTICS_VIEW"));
                        break;
                    case "3":
                        authorities.add(new SimpleGrantedAuthority("ROLE_EDITOR"));
                        authorities.add(new SimpleGrantedAuthority("RESOURCE_EDIT"));
                        authorities.add(new SimpleGrantedAuthority("DOCUMENT_EDIT"));
                        authorities.add(new SimpleGrantedAuthority("ACTIVITY_EDIT"));
                        break;
                    default:
                        // 默认普通用户权限
                        authorities.add(new SimpleGrantedAuthority("RESOURCE_VIEW"));
                        authorities.add(new SimpleGrantedAuthority("DOCUMENT_VIEW"));
                        authorities.add(new SimpleGrantedAuthority("ACTIVITY_VIEW"));
                        break;
                }
            }
        } else {
            // 默认普通用户权限
            authorities.add(new SimpleGrantedAuthority("RESOURCE_VIEW"));
            authorities.add(new SimpleGrantedAuthority("DOCUMENT_VIEW"));
            authorities.add(new SimpleGrantedAuthority("ACTIVITY_VIEW"));
        }
        
        return authorities;
    }
}