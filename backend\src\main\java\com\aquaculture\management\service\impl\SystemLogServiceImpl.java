package com.aquaculture.management.service.impl;

import com.aquaculture.management.entity.SystemLog;
import com.aquaculture.management.mapper.SystemLogMapper;
import com.aquaculture.management.service.SystemLogService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
public class SystemLogServiceImpl extends ServiceImpl<SystemLogMapper, SystemLog> implements SystemLogService {

    @Override
    public IPage<SystemLog> getLogs(int current, int size, String keyword, String operation, Integer status) {
        Page<SystemLog> page = new Page<>(current, size);
        LambdaQueryWrapper<SystemLog> wrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(keyword)) {
            wrapper.like(SystemLog::getUsername, keyword)
                   .or()
                   .like(SystemLog::getOperation, keyword)
                   .or()
                   .like(SystemLog::getIp, keyword);
        }

        if (StringUtils.hasText(operation)) {
            wrapper.like(SystemLog::getOperation, operation);
        }

        if (status != null) {
            wrapper.eq(SystemLog::getStatus, status);
        }

        wrapper.orderByDesc(SystemLog::getCreateTime);
        return this.page(page, wrapper);
    }

    @Override
    public void clearLogs(int days) {
        LambdaQueryWrapper<SystemLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.lt(SystemLog::getCreateTime, LocalDateTime.now().minusDays(days));
        this.remove(wrapper);
    }
}