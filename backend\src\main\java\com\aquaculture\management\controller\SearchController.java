package com.aquaculture.management.controller;

import com.aquaculture.management.dto.Result;
import com.aquaculture.management.service.SearchService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Tag(name = "全文搜索")
@RestController
@RequestMapping("/search")
@RequiredArgsConstructor
public class SearchController {

    private final SearchService searchService;

    @Operation(summary = "全文搜索")
    @GetMapping
    public Result<IPage<Map<String, Object>>> search(@RequestParam String keyword,
                                                   @RequestParam(required = false) String type,
                                                   @RequestParam(required = false) String region,
                                                   @RequestParam(required = false) String period,
                                                   @RequestParam(required = false) String sortBy,
                                                   @RequestParam(defaultValue = "1") Integer current,
                                                   @RequestParam(defaultValue = "10") Integer size) {
        return searchService.search(keyword, type, region, period, sortBy, current, size);
    }

    @Operation(summary = "搜索建议")
    @GetMapping("/suggestions")
    public Result<Object> getSearchSuggestions(@RequestParam String keyword) {
        return searchService.getSearchSuggestions(keyword);
    }

    @Operation(summary = "热门搜索关键词")
    @GetMapping("/hot-keywords")
    public Result<Object> getHotKeywords() {
        return searchService.getHotKeywords();
    }

    @Operation(summary = "搜索统计")
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getSearchStatistics() {
        return searchService.getSearchStatistics();
    }
}