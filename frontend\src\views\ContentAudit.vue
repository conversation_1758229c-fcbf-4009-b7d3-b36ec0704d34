<template>
  <div class="content-audit">
    <div class="audit-header">
      <div class="header-left">
        <h2>内容审核管理</h2>
        <div class="audit-stats">
          <el-tag type="warning" size="large">
            待审核: {{ statistics.pending || 0 }}
          </el-tag>
          <el-tag type="success" size="large">
            已通过: {{ statistics.approved || 0 }}
          </el-tag>
          <el-tag type="danger" size="large">
            已拒绝: {{ statistics.rejected || 0 }}
          </el-tag>
        </div>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="success" @click="showBatchDialog" :disabled="selectedAudits.length === 0">
          批量审核
        </el-button>
      </div>
    </div>

    <div class="audit-filters">
      <el-form :model="searchForm" inline>
        <el-form-item label="内容类型">
          <el-select v-model="searchForm.contentType" placeholder="选择内容类型" clearable>
            <el-option label="全部" value="" />
            <el-option label="文化资源" value="cultural_resource" />
            <el-option label="文档资料" value="document" />
            <el-option label="活动" value="activity" />
          </el-select>
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select v-model="searchForm.auditStatus" placeholder="选择审核状态" clearable>
            <el-option label="全部" value="" />
            <el-option label="待审核" :value="0" />
            <el-option label="已通过" :value="1" />
            <el-option label="已拒绝" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="searchForm.priority" placeholder="选择优先级" clearable>
            <el-option label="全部" value="" />
            <el-option label="低" :value="1" />
            <el-option label="中" :value="2" />
            <el-option label="高" :value="3" />
            <el-option label="紧急" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchAudits">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="audit-table">
      <el-table
        v-loading="loading"
        :data="auditList"
        @selection-change="handleSelectionChange"
        stripe
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="contentTitle" label="内容标题" min-width="200">
          <template #default="{ row }">
            <div class="content-title">
              <span>{{ row.contentTitle }}</span>
              <el-tag :type="getContentTypeColor(row.contentType)" size="small">
                {{ getContentTypeName(row.contentType) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="submitUserName" label="提交人" width="120" />
        <el-table-column prop="priority" label="优先级" width="80">
          <template #default="{ row }">
            <el-tag :type="getPriorityColor(row.priority)" size="small">
              {{ getPriorityName(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="auditStatus" label="审核状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.auditStatus)" size="small">
              {{ getStatusName(row.auditStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="autoAuditScore" label="自动评分" width="100">
          <template #default="{ row }">
            <el-progress
              v-if="row.autoAuditScore !== null"
              :percentage="row.autoAuditScore"
              :color="getScoreColor(row.autoAuditScore)"
              :stroke-width="8"
            />
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="submitTime" label="提交时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.submitTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewDetail(row)">详情</el-button>
            <el-button
              v-if="row.auditStatus === 0"
              size="small"
              type="success"
              @click="approveAudit(row)"
            >
              通过
            </el-button>
            <el-button
              v-if="row.auditStatus === 0"
              size="small"
              type="danger"
              @click="rejectAudit(row)"
            >
              拒绝
            </el-button>
            <el-button
              v-if="row.auditStatus === 0 && row.autoAuditScore >= 60"
              size="small"
              type="warning"
              @click="autoAudit(row)"
            >
              自动审核
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadAudits"
          @current-change="loadAudits"
        />
      </div>
    </div>

    <!-- 审核详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="审核详情" width="800px">
      <audit-detail-form
        v-if="currentAudit"
        :audit="currentAudit"
        @approve="handleApprove"
        @reject="handleReject"
        @close="showDetailDialog = false"
      />
    </el-dialog>

    <!-- 批量审核对话框 -->
    <el-dialog v-model="showBatchAuditDialog" title="批量审核" width="500px">
      <el-form :model="batchForm" label-width="100px">
        <el-form-item label="审核结果">
          <el-radio-group v-model="batchForm.auditStatus">
            <el-radio :label="1">通过</el-radio>
            <el-radio :label="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见">
          <el-input
            v-model="batchForm.auditReason"
            type="textarea"
            :rows="4"
            placeholder="请输入审核意见"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showBatchAuditDialog = false">取消</el-button>
        <el-button type="primary" @click="handleBatchAudit" :loading="batchLoading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'
import { format } from 'date-fns'
import AuditDetailForm from '@/components/ContentAudit/AuditDetailForm.vue'

const loading = ref(false)
const batchLoading = ref(false)
const showDetailDialog = ref(false)
const showBatchAuditDialog = ref(false)
const currentAudit = ref(null)
const selectedAudits = ref([])

const searchForm = reactive({
  contentType: '',
  auditStatus: '',
  priority: ''
})

const batchForm = reactive({
  auditStatus: 1,
  auditReason: ''
})

const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

const auditList = ref([])
const statistics = ref({})

const loadAudits = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    
    // 清空值为空字符串的参数
    Object.keys(params).forEach(key => {
      if (params[key] === '') {
        delete params[key]
      }
    })

    const response = await request.get('/content-audit/page', { params })
    auditList.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载审核记录失败')
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    const response = await request.get('/content-audit/statistics')
    statistics.value = response.data.summary
  } catch (error) {
    console.error('加载统计数据失败', error)
  }
}

const searchAudits = () => {
  pagination.current = 1
  loadAudits()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    contentType: '',
    auditStatus: '',
    priority: ''
  })
  searchAudits()
}

const refreshData = () => {
  loadAudits()
  loadStatistics()
}

const handleSelectionChange = (selection) => {
  selectedAudits.value = selection
}

const viewDetail = (audit) => {
  currentAudit.value = audit
  showDetailDialog.value = true
}

const approveAudit = async (audit) => {
  try {
    const { value: reason } = await ElMessageBox.prompt('请输入审核意见', '审核通过', {
      inputType: 'textarea',
      inputPlaceholder: '审核意见（可选）'
    })
    
    await request.post(`/content-audit/approve/${audit.id}`, null, {
      params: { auditReason: reason }
    })
    
    ElMessage.success('审核通过')
    loadAudits()
    loadStatistics()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('审核失败')
    }
  }
}

const rejectAudit = async (audit) => {
  try {
    const { value: reason } = await ElMessageBox.prompt('请输入拒绝原因', '审核拒绝', {
      inputType: 'textarea',
      inputPlaceholder: '拒绝原因（必填）',
      inputValidator: (value) => {
        if (!value || value.trim() === '') {
          return '请输入拒绝原因'
        }
        return true
      }
    })
    
    await request.post(`/content-audit/reject/${audit.id}`, null, {
      params: { auditReason: reason }
    })
    
    ElMessage.success('审核拒绝')
    loadAudits()
    loadStatistics()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('审核失败')
    }
  }
}

const autoAudit = async (audit) => {
  try {
    await ElMessageBox.confirm('确定要执行自动审核吗？', '自动审核', {
      type: 'warning'
    })
    
    await request.post(`/content-audit/auto/${audit.id}`)
    ElMessage.success('自动审核完成')
    loadAudits()
    loadStatistics()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('自动审核失败')
    }
  }
}

const showBatchDialog = () => {
  if (selectedAudits.value.length === 0) {
    ElMessage.warning('请选择要审核的记录')
    return
  }
  
  // 检查是否都是待审核状态
  const pendingAudits = selectedAudits.value.filter(audit => audit.auditStatus === 0)
  if (pendingAudits.length === 0) {
    ElMessage.warning('所选记录中没有待审核的内容')
    return
  }
  
  if (pendingAudits.length !== selectedAudits.value.length) {
    ElMessage.warning('请只选择待审核的记录')
    return
  }
  
  showBatchAuditDialog.value = true
}

const handleBatchAudit = async () => {
  if (!batchForm.auditReason.trim()) {
    ElMessage.warning('请输入审核意见')
    return
  }
  
  batchLoading.value = true
  try {
    const auditIds = selectedAudits.value.map(audit => audit.id)
    
    await request.post('/content-audit/batch', {
      auditIds,
      auditStatus: batchForm.auditStatus,
      auditReason: batchForm.auditReason
    })
    
    ElMessage.success('批量审核完成')
    showBatchAuditDialog.value = false
    loadAudits()
    loadStatistics()
    
    // 重置表单
    batchForm.auditReason = ''
    batchForm.auditStatus = 1
  } catch (error) {
    ElMessage.error('批量审核失败')
  } finally {
    batchLoading.value = false
  }
}

const handleApprove = async (audit, reason) => {
  try {
    await request.post(`/content-audit/approve/${audit.id}`, null, {
      params: { auditReason: reason }
    })
    
    ElMessage.success('审核通过')
    showDetailDialog.value = false
    loadAudits()
    loadStatistics()
  } catch (error) {
    ElMessage.error('审核失败')
  }
}

const handleReject = async (audit, reason) => {
  try {
    await request.post(`/content-audit/reject/${audit.id}`, null, {
      params: { auditReason: reason }
    })
    
    ElMessage.success('审核拒绝')
    showDetailDialog.value = false
    loadAudits()
    loadStatistics()
  } catch (error) {
    ElMessage.error('审核失败')
  }
}

// 辅助方法
const getContentTypeName = (type) => {
  const typeMap = {
    'cultural_resource': '文化资源',
    'document': '文档资料',
    'activity': '活动'
  }
  return typeMap[type] || type
}

const getContentTypeColor = (type) => {
  const colorMap = {
    'cultural_resource': 'primary',
    'document': 'success',
    'activity': 'warning'
  }
  return colorMap[type] || 'info'
}

const getPriorityName = (priority) => {
  const priorityMap = {
    1: '低',
    2: '中',
    3: '高',
    4: '紧急'
  }
  return priorityMap[priority] || '未知'
}

const getPriorityColor = (priority) => {
  const colorMap = {
    1: 'info',
    2: 'primary',
    3: 'warning',
    4: 'danger'
  }
  return colorMap[priority] || 'info'
}

const getStatusName = (status) => {
  const statusMap = {
    0: '待审核',
    1: '已通过',
    2: '已拒绝'
  }
  return statusMap[status] || '未知'
}

const getStatusColor = (status) => {
  const colorMap = {
    0: 'warning',
    1: 'success',
    2: 'danger'
  }
  return colorMap[status] || 'info'
}

const getScoreColor = (score) => {
  if (score >= 80) return '#67c23a'
  if (score >= 60) return '#e6a23c'
  return '#f56c6c'
}

const formatTime = (time) => {
  if (!time) return '-'
  try {
    return format(new Date(time), 'yyyy-MM-dd HH:mm:ss')
  } catch (error) {
    return time
  }
}

onMounted(() => {
  loadAudits()
  loadStatistics()
})
</script>

<style scoped>
.content-audit {
  padding: 20px;
}

.audit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 10px 0;
  color: #333;
}

.audit-stats {
  display: flex;
  gap: 10px;
}

.header-right {
  display: flex;
  gap: 10px;
}

.audit-filters {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.audit-table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.content-title {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.content-title span {
  font-weight: 500;
  color: #333;
}

.pagination {
  padding: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-table) {
  border-radius: 0;
}

:deep(.el-table th) {
  background: #f8f9fa;
}
</style>