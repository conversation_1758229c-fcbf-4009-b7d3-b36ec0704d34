package com.aquaculture.management.service;

import com.aquaculture.management.entity.CulturalResource;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

public interface CulturalResourceService extends IService<CulturalResource> {

    IPage<CulturalResource> getResources(int current, int size, String keyword, String type, String category, String region);

    boolean createResource(CulturalResource resource);

    boolean updateResource(CulturalResource resource);

    boolean deleteResource(Long id);

    void incrementViewCount(Long id);

    void incrementDownloadCount(Long id);

    List<CulturalResource> getRecommendedResources(int limit);

    List<Map<String, Object>> getResourceTypeStatistics();

    List<Map<String, Object>> getResourceRegionStatistics();
}