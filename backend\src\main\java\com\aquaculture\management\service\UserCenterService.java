package com.aquaculture.management.service;

import com.aquaculture.management.entity.UserFavorite;
import com.aquaculture.management.entity.UserFollow;
import com.aquaculture.management.entity.UserProfile;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.Map;

public interface UserCenterService {

    UserProfile getUserProfile(Long userId);

    boolean updateUserProfile(UserProfile userProfile);

    boolean createUserProfile(UserProfile userProfile);

    boolean changePassword(Long userId, String oldPassword, String newPassword);

    IPage<UserFavorite> getUserFavorites(Long userId, int current, int size, String resourceType);

    boolean addFavorite(Long userId, String resourceType, Long resourceId, String resourceTitle);

    boolean removeFavorite(Long userId, String resourceType, Long resourceId);

    boolean isResourceFavorited(Long userId, String resourceType, Long resourceId);

    IPage<UserFollow> getUserFollowers(Long userId, int current, int size);

    IPage<UserFollow> getUserFollowing(Long userId, int current, int size);

    boolean followUser(Long followerId, Long followeeId);

    boolean unfollowUser(Long followerId, Long followeeId);

    boolean isUserFollowed(Long followerId, Long followeeId);

    Map<String, Object> getUserStatistics(Long userId);
}