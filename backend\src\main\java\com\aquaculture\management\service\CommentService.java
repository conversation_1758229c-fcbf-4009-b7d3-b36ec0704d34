package com.aquaculture.management.service;

import com.aquaculture.management.dto.Result;
import com.aquaculture.management.entity.Comment;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface CommentService extends IService<Comment> {

    /**
     * 发表评论
     */
    Result<Comment> addComment(String targetType, Long targetId, String content, 
                              Long parentId, Long replyToUserId);

    /**
     * 获取评论列表
     */
    Result<IPage<Comment>> getComments(String targetType, Long targetId, 
                                     Integer current, Integer size, String sortBy);

    /**
     * 获取回复列表
     */
    Result<List<Comment>> getReplies(Long parentId);

    /**
     * 删除评论
     */
    Result<Void> deleteComment(Long commentId);

    /**
     * 点赞/取消点赞评论
     */
    Result<Void> likeComment(Long commentId, Integer likeType);

    /**
     * 置顶/取消置顶评论
     */
    Result<Void> pinComment(Long commentId, Boolean isPinned);

    /**
     * 获取评论统计
     */
    Result<Long> getCommentCount(String targetType, Long targetId);

    /**
     * 审核评论
     */
    Result<Void> auditComment(Long commentId, Integer auditStatus, String auditReason);

    /**
     * 批量删除评论
     */
    Result<Void> batchDeleteComments(List<Long> commentIds);

    /**
     * 获取用户的评论记录
     */
    Result<IPage<Comment>> getUserComments(Long userId, Integer current, Integer size);

    /**
     * 检查用户是否已点赞评论
     */
    Result<Boolean> hasLikedComment(Long commentId);
}