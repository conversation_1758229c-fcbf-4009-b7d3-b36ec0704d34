<template>
  <div class="avatar-upload">
    <div class="upload-area">
      <el-upload
        ref="uploadRef"
        class="avatar-uploader"
        :action="uploadUrl"
        :headers="uploadHeaders"
        :show-file-list="false"
        :on-success="handleSuccess"
        :on-error="handleError"
        :before-upload="beforeUpload"
        :on-progress="handleProgress"
        accept="image/*"
        drag
      >
        <div v-if="!uploading" class="upload-content">
          <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
          <div class="upload-text">
            <p>点击或拖拽图片到此区域</p>
            <p class="upload-hint">支持 JPG、PNG 格式，文件大小不超过 2MB</p>
          </div>
        </div>
        <div v-else class="uploading-content">
          <el-progress
            type="circle"
            :percentage="uploadProgress"
            :width="80"
            status="success"
          />
          <p>上传中...</p>
        </div>
      </el-upload>
    </div>

    <div v-if="previewUrl" class="preview-area">
      <h4>预览</h4>
      <div class="avatar-preview">
        <el-avatar :size="100" :src="previewUrl" />
        <div class="preview-actions">
          <el-button size="small" @click="removePreview">
            <el-icon><Delete /></el-icon>
            移除
          </el-button>
        </div>
      </div>
    </div>

    <div class="upload-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button 
        type="primary" 
        @click="handleConfirm" 
        :disabled="!previewUrl"
        :loading="confirming"
      >
        确定
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/user'
import request from '@/utils/request'

const emit = defineEmits(['success', 'cancel'])

const userStore = useUserStore()
const uploadRef = ref()
const uploading = ref(false)
const confirming = ref(false)
const uploadProgress = ref(0)
const previewUrl = ref('')

const uploadUrl = computed(() => {
  return `${import.meta.env.VITE_API_BASE_URL}/files/upload`
})

const uploadHeaders = computed(() => {
  return {
    'Authorization': `Bearer ${userStore.token}`
  }
})

const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传头像图片大小不能超过 2MB!')
    return false
  }

  uploading.value = true
  uploadProgress.value = 0
  return true
}

const handleProgress = (event) => {
  uploadProgress.value = Math.round(event.percent)
}

const handleSuccess = (response) => {
  uploading.value = false
  if (response.code === 200) {
    previewUrl.value = response.data.url
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error(response.message || '上传失败')
  }
}

const handleError = (error) => {
  uploading.value = false
  uploadProgress.value = 0
  ElMessage.error('上传失败: ' + error.message)
}

const removePreview = () => {
  previewUrl.value = ''
  uploadProgress.value = 0
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

const handleConfirm = async () => {
  if (!previewUrl.value) {
    ElMessage.warning('请先上传头像')
    return
  }

  confirming.value = true
  try {
    await request.put('/user-center/avatar', {
      avatar: previewUrl.value
    })
    
    emit('success', previewUrl.value)
  } catch (error) {
    ElMessage.error('保存头像失败: ' + error.message)
  } finally {
    confirming.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.avatar-upload {
  padding: 20px;
}

.upload-area {
  margin-bottom: 20px;
}

.avatar-uploader {
  display: block;
}

:deep(.el-upload) {
  border: 2px dashed var(--el-border-color);
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
  width: 100%;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-upload:hover) {
  border-color: var(--el-color-primary);
}

.upload-content {
  text-align: center;
  color: #666;
}

.avatar-uploader-icon {
  font-size: 48px;
  color: #8c939d;
  margin-bottom: 16px;
  display: block;
}

.upload-text p {
  margin: 8px 0;
}

.upload-hint {
  font-size: 12px;
  color: #999;
}

.uploading-content {
  text-align: center;
  color: #666;
}

.uploading-content p {
  margin-top: 16px;
  margin-bottom: 0;
}

.preview-area {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.preview-area h4 {
  margin: 0 0 16px 0;
  color: #333;
}

.avatar-preview {
  display: flex;
  align-items: center;
  gap: 20px;
}

.preview-actions {
  flex: 1;
}

.upload-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}
</style>