<template>
  <div class="dashboard">
    <div class="page-header">
      <h2>仪表盘</h2>
      <p>水族文化资源管理平台概况</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon users">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalUsers || 0 }}</div>
              <div class="stat-label">总用户数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon resources">
              <el-icon><Collection /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalResources || 0 }}</div>
              <div class="stat-label">文化资源</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon documents">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalDocuments || 0 }}</div>
              <div class="stat-label">文档资料</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon activities">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalActivities || 0 }}</div>
              <div class="stat-label">活动数量</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 快捷操作 -->
    <div class="page-content">
      <div style="padding: 20px;">
        <h3 style="margin-bottom: 20px;">快捷操作</h3>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card shadow="hover" class="quick-action-card" @click="$router.push('/cultural-resources')">
              <div class="quick-action">
                <el-icon class="action-icon"><Plus /></el-icon>
                <span>添加文化资源</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="quick-action-card" @click="$router.push('/documents')">
              <div class="quick-action">
                <el-icon class="action-icon"><DocumentAdd /></el-icon>
                <span>添加文档资料</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="quick-action-card" @click="$router.push('/activities')">
              <div class="quick-action">
                <el-icon class="action-icon"><CirclePlus /></el-icon>
                <span>创建活动</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="quick-action-card" @click="$router.push('/users')">
              <div class="quick-action">
                <el-icon class="action-icon"><UserFilled /></el-icon>
                <span>用户管理</span>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import request from '@/utils/request'

const statistics = ref({})

const loadStatistics = async () => {
  try {
    const response = await request.get('/statistics/dashboard')
    statistics.value = response.data
  } catch (error) {
    console.error('Failed to load statistics:', error)
  }
}

onMounted(() => {
  loadStatistics()
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.stat-icon.users {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.resources {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.documents {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.activities {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.quick-action-card {
  cursor: pointer;
  transition: transform 0.3s;
}

.quick-action-card:hover {
  transform: translateY(-5px);
}

.quick-action {
  text-align: center;
  padding: 20px;
}

.action-icon {
  font-size: 32px;
  color: #1890ff;
  margin-bottom: 10px;
}

.quick-action span {
  display: block;
  font-size: 16px;
  color: #333;
}
</style>