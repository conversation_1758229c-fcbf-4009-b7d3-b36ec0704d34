package com.aquaculture.management.enums;

public enum Status {
    
    ACTIVE(1, "启用"),
    INACTIVE(0, "禁用"),
    PENDING(2, "待审核"),
    APPROVED(3, "已审核"),
    REJECTED(4, "已拒绝");

    private final Integer code;
    private final String name;

    Status(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}