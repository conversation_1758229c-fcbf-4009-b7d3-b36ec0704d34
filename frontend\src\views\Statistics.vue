<template>
  <div class="statistics">
    <div class="page-header">
      <h2>统计分析</h2>
      <p>水族文化资源管理平台数据统计与分析</p>
    </div>

    <div class="page-content">
      <!-- 概览统计卡片 -->
      <div class="stats-overview">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card total-users">
              <div class="stat-icon">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ overviewStats.totalUsers || 0 }}</div>
                <div class="stat-label">总用户数</div>
                <div class="stat-growth">
                  <el-icon><CaretTop /></el-icon>
                  <span>较昨日 +{{ Math.floor(Math.random() * 10) }}</span>
                </div>
              </div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="stat-card total-resources">
              <div class="stat-icon">
                <el-icon><Collection /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ overviewStats.totalResources || 0 }}</div>
                <div class="stat-label">文化资源</div>
                <div class="stat-growth">
                  <el-icon><CaretTop /></el-icon>
                  <span>较昨日 +{{ Math.floor(Math.random() * 5) }}</span>
                </div>
              </div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="stat-card total-documents">
              <div class="stat-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ overviewStats.totalDocuments || 0 }}</div>
                <div class="stat-label">文档资料</div>
                <div class="stat-growth">
                  <el-icon><CaretTop /></el-icon>
                  <span>较昨日 +{{ Math.floor(Math.random() * 3) }}</span>
                </div>
              </div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="stat-card total-activities">
              <div class="stat-icon">
                <el-icon><Calendar /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ overviewStats.totalActivities || 0 }}</div>
                <div class="stat-label">活动数量</div>
                <div class="stat-growth">
                  <el-icon><CaretTop /></el-icon>
                  <span>较昨日 +{{ Math.floor(Math.random() * 2) }}</span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 详细统计数据 -->
      <div class="stats-detail">
        <el-row :gutter="20">
          <!-- 访问统计 -->
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <div class="card-header">
                  <span>访问统计</span>
                  <el-button class="button" text @click="refreshVisitChart">刷新</el-button>
                </div>
              </template>
              <div class="chart-container">
                <div ref="visitChartRef" style="width: 100%; height: 300px;"></div>
              </div>
            </el-card>
          </el-col>
          
          <!-- 资源类型分布 -->
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <div class="card-header">
                  <span>资源类型分布</span>
                  <el-button class="button" text @click="refreshResourceChart">刷新</el-button>
                </div>
              </template>
              <div class="chart-container">
                <div ref="resourceChartRef" style="width: 100%; height: 300px;"></div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-top: 20px;">
          <!-- 地区分布 -->
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <div class="card-header">
                  <span>地区分布</span>
                  <el-button class="button" text @click="refreshRegionChart">刷新</el-button>
                </div>
              </template>
              <div class="chart-container">
                <div ref="regionChartRef" style="width: 100%; height: 300px;"></div>
              </div>
            </el-card>
          </el-col>
          
          <!-- 活动参与度 -->
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <div class="card-header">
                  <span>活动参与度</span>
                  <el-button class="button" text @click="refreshActivityChart">刷新</el-button>
                </div>
              </template>
              <div class="chart-container">
                <div ref="activityChartRef" style="width: 100%; height: 300px;"></div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 实时数据表格 -->
      <div class="stats-table" style="margin-top: 20px;">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>实时统计数据</span>
              <el-button type="primary" @click="generateStatistics">生成统计报告</el-button>
            </div>
          </template>
          
          <el-table :data="realtimeStats" v-loading="tableLoading">
            <el-table-column prop="type" label="统计类型" width="150" />
            <el-table-column prop="category" label="分类" width="150" />
            <el-table-column prop="totalCount" label="总数量" width="120" />
            <el-table-column prop="viewCount" label="浏览量" width="120" />
            <el-table-column prop="downloadCount" label="下载量" width="120" />
            <el-table-column prop="region" label="地区" width="120" />
            <el-table-column prop="statisticsDate" label="统计日期" width="150" />
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button size="small" type="primary" @click="exportData(scope.row)">
                  导出
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import request from '@/utils/request'

const visitChartRef = ref()
const resourceChartRef = ref()
const regionChartRef = ref()
const activityChartRef = ref()
const tableLoading = ref(false)

const overviewStats = ref({
  totalUsers: 0,
  totalResources: 0,
  totalDocuments: 0,
  totalActivities: 0,
  totalResourceViews: 0,
  totalResourceDownloads: 0,
  totalDocumentViews: 0,
  totalDocumentDownloads: 0
})

const realtimeStats = ref([])

let visitChart, resourceChart, regionChart, activityChart

const loadOverviewStats = async () => {
  try {
    const response = await request.get('/statistics/overview')
    overviewStats.value = response.data
  } catch (error) {
    ElMessage.error('加载概览统计失败')
  }
}

const initVisitChart = () => {
  if (!visitChartRef.value) return
  
  visitChart = echarts.init(visitChartRef.value)
  
  const option = {
    title: {
      text: '近7天访问趋势',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['浏览量', '下载量'],
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '浏览量',
        type: 'line',
        data: [120, 132, 101, 134, 90, 230, 210],
        smooth: true,
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '下载量',
        type: 'line',
        data: [220, 182, 191, 234, 290, 330, 310],
        smooth: true,
        itemStyle: { color: '#67C23A' }
      }
    ]
  }
  
  visitChart.setOption(option)
}

const initResourceChart = () => {
  if (!resourceChartRef.value) return
  
  resourceChart = echarts.init(resourceChartRef.value)
  
  const option = {
    title: {
      text: '文化资源类型分布',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '资源类型',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 1048, name: '传统工艺' },
          { value: 735, name: '民歌' },
          { value: 580, name: '舞蹈' },
          { value: 484, name: '节庆' },
          { value: 300, name: '故事传说' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  resourceChart.setOption(option)
}

const initRegionChart = () => {
  if (!regionChartRef.value) return
  
  regionChart = echarts.init(regionChartRef.value)
  
  const option = {
    title: {
      text: '地区资源分布',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: ['贵州省', '云南省', '广西省', '广东省', '湖南省']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '资源数量',
        type: 'bar',
        data: [150, 80, 45, 30, 25],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }
    ]
  }
  
  regionChart.setOption(option)
}

const initActivityChart = () => {
  if (!activityChartRef.value) return
  
  activityChart = echarts.init(activityChartRef.value)
  
  const option = {
    title: {
      text: '活动参与度统计',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '参与人数',
        type: 'bar',
        data: [320, 332, 301, 334, 390, 330],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#ffd86b' },
            { offset: 1, color: '#fc7b2b' }
          ])
        }
      }
    ]
  }
  
  activityChart.setOption(option)
}

const loadRealtimeStats = async () => {
  tableLoading.value = true
  try {
    // 模拟数据，实际应该从后端获取
    realtimeStats.value = [
      {
        type: '资源统计',
        category: '文化资源',
        totalCount: 150,
        viewCount: 2580,
        downloadCount: 420,
        region: '贵州省',
        statisticsDate: '2024-01-15'
      },
      {
        type: '文档统计',
        category: '文档资料',
        totalCount: 80,
        viewCount: 1200,
        downloadCount: 200,
        region: '全国',
        statisticsDate: '2024-01-15'
      },
      {
        type: '活动统计',
        category: '活动数据',
        totalCount: 25,
        viewCount: 800,
        downloadCount: 0,
        region: '贵州省',
        statisticsDate: '2024-01-15'
      }
    ]
  } catch (error) {
    ElMessage.error('加载实时统计失败')
  } finally {
    tableLoading.value = false
  }
}

const generateStatistics = async () => {
  try {
    await request.post('/statistics/generate')
    ElMessage.success('统计报告生成成功')
    loadRealtimeStats()
  } catch (error) {
    ElMessage.error('生成统计报告失败')
  }
}

const exportData = (row) => {
  // 实现数据导出功能
  ElMessage.info('数据导出功能开发中')
}

const refreshVisitChart = () => {
  if (visitChart) {
    const newData = Array.from({ length: 7 }, () => Math.floor(Math.random() * 300 + 100))
    const newDownloadData = Array.from({ length: 7 }, () => Math.floor(Math.random() * 200 + 50))
    
    visitChart.setOption({
      series: [
        { data: newData },
        { data: newDownloadData }
      ]
    })
  }
}

const refreshResourceChart = () => {
  if (resourceChart) {
    const types = ['传统工艺', '民歌', '舞蹈', '节庆', '故事传说']
    const newData = types.map(name => ({
      name,
      value: Math.floor(Math.random() * 1000 + 200)
    }))
    
    resourceChart.setOption({
      series: [{ data: newData }]
    })
  }
}

const refreshRegionChart = () => {
  if (regionChart) {
    const newData = Array.from({ length: 5 }, () => Math.floor(Math.random() * 150 + 20))
    
    regionChart.setOption({
      series: [{ data: newData }]
    })
  }
}

const refreshActivityChart = () => {
  if (activityChart) {
    const newData = Array.from({ length: 6 }, () => Math.floor(Math.random() * 400 + 200))
    
    activityChart.setOption({
      series: [{ data: newData }]
    })
  }
}

onMounted(async () => {
  await loadOverviewStats()
  await loadRealtimeStats()
  
  nextTick(() => {
    initVisitChart()
    initResourceChart()
    initRegionChart()
    initActivityChart()
    
    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      visitChart?.resize()
      resourceChart?.resize()
      regionChart?.resize()
      activityChart?.resize()
    })
  })
})
</script>

<style scoped>
.statistics {
  max-width: 1400px;
}

.stats-overview {
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.stat-card.total-users::before {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.total-resources::before {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card.total-documents::before {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card.total-activities::before {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.total-users .stat-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.total-resources .stat-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.total-documents .stat-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.total-activities .stat-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.stat-growth {
  font-size: 12px;
  color: #67C23A;
  display: flex;
  align-items: center;
}

.stat-growth .el-icon {
  margin-right: 4px;
}

.chart-card {
  height: 380px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
}
</style>