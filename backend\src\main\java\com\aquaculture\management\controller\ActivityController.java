package com.aquaculture.management.controller;

import com.aquaculture.management.entity.Activity;
import com.aquaculture.management.service.ActivityService;
import com.aquaculture.management.utils.Result;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/activities")
@RequiredArgsConstructor
@CrossOrigin
public class ActivityController {

    private final ActivityService activityService;

    @GetMapping
    public Result<IPage<Activity>> getActivities(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) Integer status) {
        try {
            IPage<Activity> activities = activityService.getActivities(current, size, keyword, type, status);
            return Result.success(activities);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public Result<Activity> getActivity(@PathVariable Long id) {
        try {
            Activity activity = activityService.getById(id);
            if (activity == null) {
                return Result.error("活动不存在");
            }
            return Result.success(activity);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PostMapping
    public Result<Void> createActivity(@RequestBody @Validated Activity activity) {
        try {
            activityService.createActivity(activity);
            return Result.success("创建成功");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PutMapping("/{id}")
    public Result<Void> updateActivity(@PathVariable Long id, @RequestBody @Validated Activity activity) {
        try {
            activity.setId(id);
            activityService.updateActivity(activity);
            return Result.success("更新成功");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    public Result<Void> deleteActivity(@PathVariable Long id) {
        try {
            activityService.deleteActivity(id);
            return Result.success("删除成功");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/upcoming")
    public Result<List<Activity>> getUpcomingActivities(@RequestParam(defaultValue = "5") int limit) {
        try {
            List<Activity> activities = activityService.getUpcomingActivities(limit);
            return Result.success(activities);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/ongoing")
    public Result<List<Activity>> getOngoingActivities() {
        try {
            List<Activity> activities = activityService.getOngoingActivities();
            return Result.success(activities);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("/{id}/participate")
    public Result<Void> participateActivity(@PathVariable Long id) {
        try {
            activityService.participateActivity(id);
            return Result.success("参与成功");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("/{id}/cancel")
    public Result<Void> cancelParticipation(@PathVariable Long id) {
        try {
            activityService.cancelParticipation(id);
            return Result.success("取消参与成功");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
}