package com.aquaculture.management.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "activity")
@TableName("activity")
@EqualsAndHashCode(callSuper = true)
public class Activity extends BaseEntity {

    @NotBlank(message = "活动名称不能为空")
    private String name;

    @NotBlank(message = "活动类型不能为空")
    private String type;

    private String description;

    private String location;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    private String organizer;

    private String contact;

    private String phone;

    private String email;

    private Integer maxParticipants;

    private Integer currentParticipants;

    private String requirements;

    private String materials;

    private Integer status;

    private String coverImage;

    private String tags;

    private String fee;

    private String notes;
}