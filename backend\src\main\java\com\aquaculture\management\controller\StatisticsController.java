package com.aquaculture.management.controller;

import com.aquaculture.management.service.StatisticsService;
import com.aquaculture.management.utils.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/statistics")
@RequiredArgsConstructor
@CrossOrigin
public class StatisticsController {

    private final StatisticsService statisticsService;

    @GetMapping("/dashboard")
    public Result<Map<String, Object>> getDashboardStatistics() {
        try {
            Map<String, Object> statistics = statisticsService.getDashboardStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping("/overview")
    public Result<Map<String, Object>> getOverviewStatistics() {
        try {
            Map<String, Object> statistics = statisticsService.getOverviewStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @PostMapping("/generate")
    public Result<Void> generateDailyStatistics() {
        try {
            statisticsService.generateDailyStatistics();
            return Result.success("统计数据生成成功");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
}