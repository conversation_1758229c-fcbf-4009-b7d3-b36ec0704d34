package com.aquaculture.management.service;

import com.aquaculture.management.entity.SystemLog;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

public interface SystemLogService extends IService<SystemLog> {

    IPage<SystemLog> getLogs(int current, int size, String keyword, String operation, Integer status);

    void clearLogs(int days);
}