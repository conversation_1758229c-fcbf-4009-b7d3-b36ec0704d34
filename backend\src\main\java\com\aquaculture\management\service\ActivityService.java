package com.aquaculture.management.service;

import com.aquaculture.management.entity.Activity;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface ActivityService extends IService<Activity> {

    IPage<Activity> getActivities(int current, int size, String keyword, String type, Integer status);

    boolean createActivity(Activity activity);

    boolean updateActivity(Activity activity);

    boolean deleteActivity(Long id);

    List<Activity> getUpcomingActivities(int limit);

    List<Activity> getOngoingActivities();

    boolean participateActivity(Long activityId);

    boolean cancelParticipation(Long activityId);
}