package com.aquaculture.management.controller;

import com.aquaculture.management.dto.Result;
import com.aquaculture.management.entity.Comment;
import com.aquaculture.management.service.CommentService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@Tag(name = "评论管理")
@RestController
@RequestMapping("/comments")
@RequiredArgsConstructor
public class CommentController {

    private final CommentService commentService;

    @Operation(summary = "发表评论")
    @PostMapping
    public Result<Comment> addComment(@RequestBody Map<String, Object> request) {
        String targetType = (String) request.get("targetType");
        Long targetId = Long.valueOf(request.get("targetId").toString());
        String content = (String) request.get("content");
        Long parentId = request.get("parentId") != null ? Long.valueOf(request.get("parentId").toString()) : null;
        Long replyToUserId = request.get("replyToUserId") != null ? Long.valueOf(request.get("replyToUserId").toString()) : null;

        return commentService.addComment(targetType, targetId, content, parentId, replyToUserId);
    }

    @Operation(summary = "获取评论列表")
    @GetMapping("/{targetType}/{targetId}")
    public Result<IPage<Comment>> getComments(@PathVariable String targetType,
                                            @PathVariable Long targetId,
                                            @RequestParam(defaultValue = "1") Integer current,
                                            @RequestParam(defaultValue = "10") Integer size,
                                            @RequestParam(defaultValue = "time") String sortBy) {
        return commentService.getComments(targetType, targetId, current, size, sortBy);
    }

    @Operation(summary = "获取回复列表")
    @GetMapping("/{commentId}/replies")
    public Result<List<Comment>> getReplies(@PathVariable Long commentId) {
        return commentService.getReplies(commentId);
    }

    @Operation(summary = "删除评论")
    @DeleteMapping("/{commentId}")
    public Result<Void> deleteComment(@PathVariable Long commentId) {
        return commentService.deleteComment(commentId);
    }

    @Operation(summary = "点赞评论")
    @PostMapping("/{commentId}/like")
    public Result<Void> likeComment(@PathVariable Long commentId,
                                   @RequestParam(defaultValue = "1") Integer likeType) {
        return commentService.likeComment(commentId, likeType);
    }

    @Operation(summary = "置顶评论")
    @PostMapping("/{commentId}/pin")
    public Result<Void> pinComment(@PathVariable Long commentId,
                                  @RequestBody Map<String, Boolean> request) {
        Boolean isPinned = request.get("isPinned");
        return commentService.pinComment(commentId, isPinned);
    }

    @Operation(summary = "获取评论数量")
    @GetMapping("/{targetType}/{targetId}/count")
    public Result<Long> getCommentCount(@PathVariable String targetType,
                                       @PathVariable Long targetId) {
        return commentService.getCommentCount(targetType, targetId);
    }

    @Operation(summary = "审核评论")
    @PostMapping("/{commentId}/audit")
    public Result<Void> auditComment(@PathVariable Long commentId,
                                    @RequestParam Integer auditStatus,
                                    @RequestParam(required = false) String auditReason) {
        return commentService.auditComment(commentId, auditStatus, auditReason);
    }

    @Operation(summary = "批量删除评论")
    @DeleteMapping("/batch")
    public Result<Void> batchDeleteComments(@RequestBody List<Long> commentIds) {
        return commentService.batchDeleteComments(commentIds);
    }

    @Operation(summary = "获取用户评论")
    @GetMapping("/user/{userId}")
    public Result<IPage<Comment>> getUserComments(@PathVariable Long userId,
                                                @RequestParam(defaultValue = "1") Integer current,
                                                @RequestParam(defaultValue = "10") Integer size) {
        return commentService.getUserComments(userId, current, size);
    }

    @Operation(summary = "检查用户是否已点赞")
    @GetMapping("/{commentId}/liked")
    public Result<Boolean> hasLikedComment(@PathVariable Long commentId) {
        return commentService.hasLikedComment(commentId);
    }

    @Operation(summary = "举报评论")
    @PostMapping("/{commentId}/report")
    public Result<Void> reportComment(@PathVariable Long commentId,
                                     @RequestBody Map<String, String> request) {
        String reason = request.get("reason");
        // 这里可以实现举报逻辑，比如记录到举报表中
        // 暂时返回成功
        return Result.success();
    }
}