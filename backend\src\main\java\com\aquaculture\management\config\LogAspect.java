package com.aquaculture.management.config;

import com.aquaculture.management.entity.SystemLog;
import com.aquaculture.management.service.SystemLogService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class LogAspect {

    private final SystemLogService systemLogService;
    private final ObjectMapper objectMapper;

    /**
     * 系统日志注解
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    public @interface SystemLogAnnotation {
        String value() default "";
    }

    /**
     * 环绕通知，记录操作日志
     */
    @Around("@annotation(systemLog)")
    public Object around(ProceedingJoinPoint joinPoint, SystemLogAnnotation systemLog) throws Throwable {
        long startTime = System.currentTimeMillis();
        SystemLog logEntity = new SystemLog();
        
        try {
            // 获取请求信息
            HttpServletRequest request = getHttpServletRequest();
            if (request != null) {
                setRequestInfo(logEntity, request, joinPoint, systemLog);
            }
            
            // 执行方法
            Object result = joinPoint.proceed();
            
            // 计算执行时间
            long endTime = System.currentTimeMillis();
            logEntity.setTime(endTime - startTime);
            logEntity.setStatus(1); // 成功
            
            // 异步保存日志
            saveLogAsync(logEntity);
            
            return result;
            
        } catch (Exception e) {
            // 记录异常信息
            long endTime = System.currentTimeMillis();
            logEntity.setTime(endTime - startTime);
            logEntity.setStatus(0); // 失败
            logEntity.setErrorMsg(e.getMessage());
            
            // 异步保存日志
            saveLogAsync(logEntity);
            
            throw e;
        }
    }

    /**
     * 异常通知，记录异常日志
     */
    @AfterThrowing(pointcut = "execution(* com.aquaculture.management.controller..*.*(..))", throwing = "e")
    public void afterThrowing(JoinPoint joinPoint, Exception e) {
        try {
            SystemLog logEntity = new SystemLog();
            HttpServletRequest request = getHttpServletRequest();
            
            if (request != null) {
                setBasicRequestInfo(logEntity, request, joinPoint);
                logEntity.setOperation("系统异常");
                logEntity.setStatus(0);
                logEntity.setErrorMsg(e.getMessage());
                
                saveLogAsync(logEntity);
            }
        } catch (Exception ex) {
            log.error("记录异常日志失败", ex);
        }
    }

    /**
     * 设置请求信息
     */
    private void setRequestInfo(SystemLog logEntity, HttpServletRequest request, 
                               ProceedingJoinPoint joinPoint, SystemLogAnnotation systemLog) {
        setBasicRequestInfo(logEntity, request, joinPoint);
        
        // 设置操作描述
        String operation = systemLog.value();
        if (operation.isEmpty()) {
            operation = joinPoint.getSignature().getName();
        }
        logEntity.setOperation(operation);
        
        // 设置请求参数
        try {
            Object[] args = joinPoint.getArgs();
            if (args != null && args.length > 0) {
                // 过滤敏感参数
                String params = objectMapper.writeValueAsString(filterSensitiveParams(args));
                if (params.length() > 2000) {
                    params = params.substring(0, 2000) + "...";
                }
                logEntity.setParams(params);
            }
        } catch (Exception e) {
            logEntity.setParams("参数序列化失败");
        }
    }

    /**
     * 设置基本请求信息
     */
    private void setBasicRequestInfo(SystemLog logEntity, HttpServletRequest request, JoinPoint joinPoint) {
        // 设置用户信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() 
            && !"anonymousUser".equals(authentication.getPrincipal())) {
            logEntity.setUsername(authentication.getName());
        }
        
        // 设置请求信息
        logEntity.setMethod(request.getMethod() + " " + request.getRequestURI());
        logEntity.setIp(getClientIpAddress(request));
        logEntity.setUserAgent(request.getHeader("User-Agent"));
        
        // 简单的地理位置（实际应用中可以使用IP地址库）
        logEntity.setLocation("未知");
    }

    /**
     * 过滤敏感参数
     */
    private Object[] filterSensitiveParams(Object[] args) {
        // 这里可以实现敏感参数过滤逻辑，比如密码字段
        // 简单实现：直接返回参数
        return args;
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 获取HttpServletRequest
     */
    private HttpServletRequest getHttpServletRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes != null ? attributes.getRequest() : null;
    }

    /**
     * 异步保存日志
     */
    private void saveLogAsync(SystemLog logEntity) {
        try {
            systemLogService.save(logEntity);
        } catch (Exception e) {
            log.error("保存系统日志失败", e);
        }
    }
}